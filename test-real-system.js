/**
 * 基于真实系统的完整测试
 * 使用您的真实开场白、业务逻辑和数据库职位
 */

// 手动加载环境变量
const fs = require("fs");
const path = require("path");

try {
  const envPath = path.join(__dirname, ".env");
  const envContent = fs.readFileSync(envPath, "utf8");

  envContent.split("\n").forEach((line) => {
    const [key, value] = line.split("=");
    if (key && value) {
      process.env[key.trim()] = value.trim();
    }
  });
} catch (error) {
  console.log("⚠️ 无法读取.env文件");
}

// 导入您的真实系统组件
const MessageProcessor = require("./ai-recruitment-assistant/core/系统核心/message-processor");
const DatabaseManager = require("./ai-recruitment-assistant/core/数据管理/database-manager");
const UserManager = require("./ai-recruitment-assistant/core/业务服务/user-manager");
const AppConfig = require("./ai-recruitment-assistant/core/系统核心/app-config");

// 等待函数
function wait(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

// 显示时间戳
function getTimestamp() {
  return new Date().toLocaleTimeString();
}

async function testRealSystem() {
  console.log("🎬 开始基于真实系统的完整测试");
  console.log("时间:", getTimestamp());
  console.log("=" * 80);

  // 初始化真实系统组件
  console.log("\n📱 正在初始化真实系统组件...");

  try {
    // 初始化配置
    const config = new AppConfig({
      database: {
        supabaseUrl: process.env.SUPABASE_URL,
        supabaseKey: process.env.SUPABASE_ANON_KEY,
      },
      ai: {
        deepseekEndpoint: process.env.DEEPSEEK_ENDPOINT,
        deepseekApiKey: process.env.DEEPSEEK_API_KEY,
        qwenEndpoint: process.env.QWEN_ENDPOINT,
        qwenApiKey: process.env.QWEN_API_KEY,
        maxTokens: 2000,
        temperature: 0.7,
        timeout: 30000,
        dailyCostLimit: 50,
      },
    });
    await config.initialize();
    console.log("✅ 配置初始化成功");

    // 初始化数据库
    const database = new DatabaseManager(config);
    await database.initialize();
    console.log("✅ 数据库连接成功");

    // 初始化用户管理器
    const userManager = new UserManager(database, config);
    await userManager.initialize();
    console.log("✅ 用户管理器初始化成功");

    // 初始化消息处理器
    const messageProcessor = new MessageProcessor({
      database,
      userManager,
      config,
    });
    await messageProcessor.initialize();
    console.log("✅ 消息处理器初始化成功");
  } catch (error) {
    console.error("❌ 系统初始化失败:", error);
    return;
  }

  // 开始真实对话测试
  console.log("\n" + "=".repeat(80));
  console.log("🎭 真实系统对话测试 - 挤牙膏型候选人");
  console.log("=".repeat(80));

  // 1. 获取开场白
  console.log(`\n[${getTimestamp()}] 📱 用户打开聊天界面`);
  console.log("🔄 系统正在生成开场白...");

  try {
    const initResponse = await messageProcessor.processMessage({
      message: "__INIT__",
      sessionId: null,
      userId: null,
    });

    console.log(`\n[${getTimestamp()}] 🤖 AI猎头Katrina:`);
    console.log(initResponse.response.content);
    console.log(`\n📊 响应类型: ${initResponse.response.type}`);
    console.log(`📊 意图识别: ${initResponse.intent}`);
    console.log(`📊 会话ID: ${initResponse.sessionId}`);

    const sessionId = initResponse.sessionId;

    // 模拟用户思考时间
    console.log("\n⏳ 候选人正在阅读开场白...");
    await wait(3000);

    // 2. 第一轮对话 - 挤牙膏型回复
    console.log(`\n[${getTimestamp()}] 👤 候选人:`);
    console.log("嗯");

    console.log("\n🔄 系统正在处理消息...");
    console.log("   - 保存用户消息到数据库");
    console.log("   - 分析用户意图");
    console.log("   - 调用候选人类型分析API");
    console.log("   - 生成智能回复");

    const response1Start = Date.now();

    const response1 = await messageProcessor.processMessage({
      message: "嗯",
      sessionId: sessionId,
      userId: null,
    });

    const response1Time = Date.now() - response1Start;

    console.log(
      `\n[${getTimestamp()}] ✅ 第1轮处理完成 (耗时: ${response1Time}ms)`
    );
    console.log(`🤖 AI猎头Katrina:`);
    console.log(response1.response.content);
    console.log(`\n📊 响应类型: ${response1.response.type}`);
    console.log(`📊 意图识别: ${response1.intent}`);
    console.log(`📊 处理状态: ${response1.success ? "成功" : "失败"}`);

    // 模拟用户思考
    console.log("\n⏳ 候选人正在思考...");
    await wait(4000);

    // 3. 第二轮对话 - 继续挤牙膏
    console.log(`\n[${getTimestamp()}] 👤 候选人:`);
    console.log("看看");

    console.log("\n🔄 系统正在处理消息...");
    console.log("   - 检查候选人类型缓存");
    console.log("   - 更新行为模式分析");
    console.log("   - 调整回复策略");

    const response2Start = Date.now();

    const response2 = await messageProcessor.processMessage({
      message: "看看",
      sessionId: sessionId,
      userId: null,
    });

    const response2Time = Date.now() - response2Start;

    console.log(
      `\n[${getTimestamp()}] ✅ 第2轮处理完成 (耗时: ${response2Time}ms)`
    );
    console.log(`🤖 AI猎头Katrina:`);
    console.log(response2.response.content);
    console.log(`\n📊 响应类型: ${response2.response.type}`);
    console.log(`📊 意图识别: ${response2.intent}`);

    // 检查是否有建议选项
    if (response2.response.suggestions) {
      console.log("💡 建议选项:");
      response2.response.suggestions.forEach((suggestion, index) => {
        console.log(`   ${index + 1}. ${suggestion}`);
      });
    }

    // 模拟用户思考
    console.log("\n⏳ 候选人正在思考...");
    await wait(3500);

    // 4. 第三轮对话 - 提供一点信息
    console.log(`\n[${getTimestamp()}] 👤 候选人:`);
    console.log("算法");

    console.log("\n🔄 系统正在处理消息...");
    console.log("   - 识别技术方向信息");
    console.log("   - 更新候选人档案");
    console.log("   - 查询相关职位");
    console.log("   - 准备职位推荐");

    const response3Start = Date.now();

    const response3 = await messageProcessor.processMessage({
      message: "算法",
      sessionId: sessionId,
      userId: null,
    });

    const response3Time = Date.now() - response3Start;

    console.log(
      `\n[${getTimestamp()}] ✅ 第3轮处理完成 (耗时: ${response3Time}ms)`
    );
    console.log(`🤖 AI猎头Katrina:`);
    console.log(response3.response.content);
    console.log(`\n📊 响应类型: ${response3.response.type}`);
    console.log(`📊 意图识别: ${response3.intent}`);

    // 检查是否有职位推荐
    if (
      response3.response.metadata &&
      response3.response.metadata.jobRecommendations
    ) {
      console.log("\n💼 职位推荐:");
      response3.response.metadata.jobRecommendations.forEach((job, index) => {
        console.log(
          `   ${index + 1}. ${job.title} - ${job.company} (${job.salary})`
        );
      });
    }

    // 最终统计
    console.log("\n" + "=".repeat(80));
    console.log("📈 真实系统测试总结");
    console.log("=".repeat(80));

    console.log(`⏰ 测试结束时间: ${getTimestamp()}`);
    console.log(`💬 总对话轮次: 3轮`);
    console.log(`📊 会话ID: ${sessionId}`);
    console.log(
      `⚡ 平均响应时间: ${(
        (response1Time + response2Time + response3Time) /
        3
      ).toFixed(0)}ms`
    );

    // 查询数据库中的对话记录
    console.log("\n📜 数据库中的完整对话记录:");
    console.log("-".repeat(60));

    try {
      const messages = await database.getChatHistory(sessionId);
      messages.forEach((msg, index) => {
        const speaker =
          msg.message_type === "assistant" ? "🤖 AI" : "👤 候选人";
        const time = new Date(msg.created_at).toLocaleTimeString();
        console.log(`${index + 1}. [${time}] ${speaker}: ${msg.content}`);
      });
    } catch (error) {
      console.log("❌ 无法获取对话记录:", error.message);
    }

    console.log("\n✅ 基于真实系统的测试完成！");
  } catch (error) {
    console.error("❌ 对话测试失败:", error);
  }
}

// 运行测试
if (require.main === module) {
  testRealSystem().catch(console.error);
}

module.exports = { testRealSystem };
