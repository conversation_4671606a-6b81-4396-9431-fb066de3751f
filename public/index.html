<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI招聘助手 - 双模型架构测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .chat-container {
            width: 800px;
            height: 600px;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        
        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .chat-header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }
        
        .chat-header p {
            opacity: 0.9;
            font-size: 14px;
        }
        
        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }
        
        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }
        
        .message.user {
            justify-content: flex-end;
        }
        
        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            font-size: 14px;
            line-height: 1.4;
        }
        
        .message.assistant .message-content {
            background: white;
            border: 1px solid #e1e5e9;
            margin-left: 10px;
        }
        
        .message.user .message-content {
            background: #667eea;
            color: white;
            margin-right: 10px;
        }
        
        .message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            font-weight: bold;
        }
        
        .message.assistant .message-avatar {
            background: #667eea;
            color: white;
        }
        
        .message.user .message-avatar {
            background: #28a745;
            color: white;
        }
        
        .message-meta {
            font-size: 11px;
            color: #666;
            margin-top: 5px;
            text-align: right;
        }
        
        .message.assistant .message-meta {
            text-align: left;
        }
        
        .chat-input {
            padding: 20px;
            background: white;
            border-top: 1px solid #e1e5e9;
            display: flex;
            gap: 10px;
        }
        
        .chat-input input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #e1e5e9;
            border-radius: 25px;
            font-size: 14px;
            outline: none;
        }
        
        .chat-input input:focus {
            border-color: #667eea;
        }
        
        .chat-input button {
            padding: 12px 24px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 25px;
            font-size: 14px;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .chat-input button:hover {
            background: #5a6fd8;
        }
        
        .chat-input button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .stats-panel {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            font-size: 12px;
            min-width: 200px;
        }
        
        .stats-panel h3 {
            margin-bottom: 10px;
            color: #333;
        }
        
        .stats-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        
        .loading {
            display: flex;
            align-items: center;
            gap: 5px;
            color: #666;
            font-style: italic;
        }
        
        .loading::after {
            content: '';
            width: 12px;
            height: 12px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <h1>AI招聘助手 Katrina</h1>
            <p>双模型架构测试 - DeepSeek V3 + Qwen-Turbo</p>
        </div>
        
        <div class="chat-messages" id="messages">
            <!-- 消息将在这里显示 -->
        </div>
        
        <div class="chat-input">
            <input type="text" id="messageInput" placeholder="输入您的消息..." maxlength="500">
            <button id="sendButton" onclick="sendMessage()">发送</button>
        </div>
    </div>
    
    <div class="stats-panel" id="statsPanel">
        <h3>📊 实时统计</h3>
        <div class="stats-item">
            <span>会话ID:</span>
            <span id="sessionId">-</span>
        </div>
        <div class="stats-item">
            <span>消息数:</span>
            <span id="messageCount">0</span>
        </div>
        <div class="stats-item">
            <span>候选人类型:</span>
            <span id="candidateType">分析中...</span>
        </div>
        <div class="stats-item">
            <span>Qwen调用:</span>
            <span id="qwenCalls">0</span>
        </div>
        <div class="stats-item">
            <span>缓存命中:</span>
            <span id="cacheHits">0</span>
        </div>
        <div class="stats-item">
            <span>总费用:</span>
            <span id="totalCost">0.000000元</span>
        </div>
    </div>

    <script>
        let sessionId = null;
        let messageCount = 0;
        
        // 初始化聊天
        async function initChat() {
            try {
                const response = await fetch('/api/init');
                const data = await response.json();
                
                if (data.success) {
                    sessionId = data.sessionId;
                    document.getElementById('sessionId').textContent = sessionId.substring(0, 8) + '...';
                    addMessage('assistant', data.message.content, data.message.timestamp);
                    updateStats();
                }
            } catch (error) {
                console.error('初始化失败:', error);
                addMessage('system', '系统初始化失败，请刷新页面重试。');
            }
        }
        
        // 发送消息
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const sendButton = document.getElementById('sendButton');
            const message = input.value.trim();
            
            if (!message || !sessionId) return;
            
            // 禁用输入
            input.disabled = true;
            sendButton.disabled = true;
            
            // 显示用户消息
            addMessage('user', message);
            input.value = '';
            messageCount++;
            
            // 显示加载状态
            const loadingId = addMessage('assistant', '', null, true);
            
            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        message: message,
                        sessionId: sessionId
                    })
                });
                
                const data = await response.json();
                
                // 移除加载状态
                document.getElementById(loadingId).remove();
                
                if (data.success) {
                    addMessage('assistant', data.message.content, data.message.timestamp, false, data.message.metadata);
                    messageCount++;
                    updateStats();
                } else {
                    addMessage('system', '发送失败: ' + data.error);
                }
                
            } catch (error) {
                document.getElementById(loadingId).remove();
                addMessage('system', '网络错误，请重试。');
                console.error('发送消息失败:', error);
            }
            
            // 重新启用输入
            input.disabled = false;
            sendButton.disabled = false;
            input.focus();
        }
        
        // 添加消息到界面
        function addMessage(role, content, timestamp, isLoading = false, metadata = null) {
            const messagesContainer = document.getElementById('messages');
            const messageId = 'msg-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
            
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}`;
            messageDiv.id = messageId;
            
            let avatarText = '';
            if (role === 'assistant') avatarText = 'AI';
            else if (role === 'user') avatarText = '我';
            else avatarText = '⚠️';
            
            let metaInfo = '';
            if (metadata) {
                metaInfo = `<div class="message-meta">
                    类型: ${metadata.candidateType} | 
                    置信度: ${(metadata.confidence * 100).toFixed(1)}% | 
                    耗时: ${metadata.analysisTime}ms | 
                    ${metadata.source}
                </div>`;
            } else if (timestamp) {
                metaInfo = `<div class="message-meta">${new Date(timestamp).toLocaleTimeString()}</div>`;
            }
            
            messageDiv.innerHTML = `
                ${role !== 'user' ? `<div class="message-avatar">${avatarText}</div>` : ''}
                <div class="message-content">
                    ${isLoading ? '<div class="loading">AI正在思考</div>' : content}
                    ${metaInfo}
                </div>
                ${role === 'user' ? `<div class="message-avatar">${avatarText}</div>` : ''}
            `;
            
            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
            
            return messageId;
        }
        
        // 更新统计信息
        async function updateStats() {
            if (!sessionId) return;
            
            try {
                const response = await fetch(`/api/stats/${sessionId}`);
                const data = await response.json();
                
                if (data.success) {
                    const stats = data.stats;
                    document.getElementById('messageCount').textContent = stats.messageCount;
                    document.getElementById('candidateType').textContent = 
                        stats.candidateType ? stats.candidateType.type : '分析中...';
                    document.getElementById('qwenCalls').textContent = stats.apiCalls.qwen;
                    document.getElementById('cacheHits').textContent = stats.apiCalls.cacheHits;
                    document.getElementById('totalCost').textContent = stats.cost.toFixed(6) + '元';
                }
            } catch (error) {
                console.error('更新统计失败:', error);
            }
        }
        
        // 回车发送
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
        
        // 页面加载时初始化
        window.onload = function() {
            initChat();
            // 定期更新统计
            setInterval(updateStats, 5000);
        };
    </script>
</body>
</html>
