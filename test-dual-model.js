/**
 * 双模型架构测试脚本
 *
 * 测试候选人类型分析和费用监控功能
 */

const AIServices = require("./ai-recruitment-assistant/core/数据管理/ai-services");
const UserManager = require("./ai-recruitment-assistant/core/业务服务/user-manager");
const AppConfig = require("./ai-recruitment-assistant/core/系统核心/app-config");

// 模拟配置
const testConfig = {
  deepseekEndpoint: "https://api.deepseek.com",
  deepseekApiKey: "test-key",
  qwenEndpoint: "https://dashscope.aliyuncs.com",
  qwenApiKey: "test-key",
  maxTokens: 2000,
  temperature: 0.7,
  timeout: 30000,
  dailyCostLimit: 50, // 50元/天限制
};

// 模拟对话数据
const testDialogues = {
  挤牙膏型: [
    {
      role: "assistant",
      content: "您好，我是AI领域的猎头Katrina，专注于AI算法职位。",
    },
    { role: "user", content: "你好" },
    { role: "assistant", content: "您考虑看看新机会吗？优质的职位还挺多的。" },
    { role: "user", content: "嗯" },
    {
      role: "assistant",
      content:
        "好的，我们当前合作了很多公司，包含头部大厂、中型公司、创业型团队、国央企等等，职位也比较多。所以得麻烦您，告知我您的技术栈或技术方向、当前所在公司、当前的职级、期望的薪资？这样可以更准确的给您推荐合适的职位。",
    },
    { role: "user", content: "算法的有吗" },
  ],

  疯狂咨询型: [
    {
      role: "assistant",
      content: "您好，我是AI领域的猎头Katrina，专注于AI算法职位。",
    },
    { role: "user", content: "你好" },
    { role: "assistant", content: "您考虑看看新机会吗？优质的职位还挺多的。" },
    { role: "user", content: "你有什么职位推荐吗" },
    {
      role: "assistant",
      content:
        "好的，我们当前合作了很多公司，包含头部大厂、中型公司、创业型团队、国央企等等，职位也比较多。所以得麻烦您，告知我您的技术栈或技术方向、当前所在公司、当前的职级、期望的薪资？这样可以更准确的给您推荐合适的职位。",
    },
    { role: "user", content: "你那边具体合作了什么公司呢？" },
  ],

  "质疑/不信任型": [
    {
      role: "assistant",
      content: "您好，我是AI领域的猎头Katrina，专注于AI算法职位。",
    },
    { role: "user", content: "你好" },
    { role: "assistant", content: "您考虑看看新机会吗？优质的职位还挺多的。" },
    { role: "user", content: "你有什么职位推荐吗" },
    {
      role: "assistant",
      content:
        "好的，我们当前合作了很多公司，包含头部大厂、中型公司、创业型团队、国央企等等，职位也比较多。所以得麻烦您，告知我您的技术栈或技术方向、当前所在公司、当前的职级、期望的薪资？这样可以更准确的给您推荐合适的职位。",
    },
    { role: "user", content: "你那边具体合作了什么公司呢？我在腾讯。" },
    {
      role: "assistant",
      content:
        "公司真的很多，国内知名的科技公司，超过一半都合作了。还得拜托您提供一下您的技术栈或技术方向、当前的职级、和期望的薪资？",
    },
    { role: "user", content: "公司1有职位吗？他们不是在裁员吗。" },
  ],
};

async function testDualModelArchitecture() {
  console.log("🚀 开始测试双模型架构...\n");

  // 初始化AI服务
  const aiServices = new AIServices(testConfig);
  await aiServices.initialize();

  console.log("📊 初始监控状态:");
  console.log(JSON.stringify(aiServices.getMonitoringStatus(), null, 2));
  console.log("\n" + "=".repeat(50) + "\n");

  // 测试不同类型的候选人分析
  for (const [type, dialogue] of Object.entries(testDialogues)) {
    console.log(`🔍 测试候选人类型: ${type}`);

    try {
      // 分析候选人类型
      const analysis = await aiServices.analyzeCandidateType(
        dialogue,
        `test-session-${type}`,
        false
      );

      console.log(`✅ 分析结果:`, analysis);

      // 测试缓存命中
      const cachedAnalysis = await aiServices.analyzeCandidateType(
        dialogue,
        `test-session-${type}`,
        false
      );

      console.log(
        `💾 缓存测试: ${cachedAnalysis.source === "cache" ? "命中" : "未命中"}`
      );
    } catch (error) {
      console.error(`❌ 测试失败:`, error.message);
    }

    console.log("\n" + "-".repeat(30) + "\n");
  }

  // 测试费用监控
  console.log("💰 测试费用监控...");

  // 模拟大量调用
  for (let i = 0; i < 10; i++) {
    aiServices.updateCostTracking("qwen", 200, 50);
    aiServices.updateCostTracking("deepseek", 200, 100);
  }

  console.log("📈 费用监控状态:");
  const monitoringStatus = aiServices.getMonitoringStatus();
  console.log(
    `当前费用: ${monitoringStatus.costMonitor.currentDailyCost.toFixed(4)}元`
  );
  console.log(
    `使用率: ${(monitoringStatus.costMonitor.usageRatio * 100).toFixed(1)}%`
  );
  console.log(
    `是否被阻止: ${monitoringStatus.costMonitor.isBlocked ? "是" : "否"}`
  );

  console.log("\n" + "=".repeat(50) + "\n");

  // 测试模型健康检查
  console.log("🏥 测试模型健康检查...");

  const qwenHealth = await aiServices.checkModelHealth("qwen");
  const deepseekHealth = await aiServices.checkModelHealth("deepseek");

  console.log(`Qwen模型健康: ${qwenHealth ? "✅" : "❌"}`);
  console.log(`DeepSeek模型健康: ${deepseekHealth ? "✅" : "❌"}`);

  console.log("\n" + "=".repeat(50) + "\n");

  // 测试容灾回复功能
  console.log("🛡️ 测试容灾回复功能...");

  const appConfig = new AppConfig();
  await appConfig.initialize();

  const testMessages = [
    { message: "你好", type: "正常型", expected: "greeting" },
    { message: "有什么职位", type: "疯狂咨询型", expected: "jobInquiry" },
    { message: "不需要", type: "挤牙膏型", expected: "decline" },
    { message: "嗯", type: "挤牙膏型", expected: "confirmation" },
  ];

  for (const test of testMessages) {
    const fallbackReply = appConfig.generateFallbackReply(
      test.message,
      test.type,
      { messageCount: 3 }
    );

    console.log(`消息: "${test.message}" | 类型: ${test.type}`);
    console.log(`回复: ${fallbackReply.content}`);
    console.log(
      `场景: ${fallbackReply.scenario} | 来源: ${fallbackReply.source}`
    );
    console.log("-".repeat(30));
  }

  // 最终统计
  console.log("\n📊 最终统计信息:");
  const finalStats = aiServices.getStats();
  console.log(`总请求数: ${finalStats.totalRequests}`);
  console.log(`Qwen调用数: ${finalStats.qwenCalls}`);
  console.log(`DeepSeek调用数: ${finalStats.deepseekCalls}`);
  console.log(`缓存命中数: ${finalStats.cacheHits}`);

  const cacheHitRate =
    finalStats.cacheHits / (finalStats.qwenCalls + finalStats.cacheHits) || 0;
  console.log(`缓存命中率: ${(cacheHitRate * 100).toFixed(1)}%`);

  console.log("\n" + "=".repeat(50) + "\n");

  // 测试14天统一缓存策略
  console.log("📅 测试14天统一缓存策略...");

  // 模拟不同时间的缓存测试
  const testCacheScenarios = [
    { sessionId: "test-session-1", rounds: 3, description: "早期对话" },
    { sessionId: "test-session-2", rounds: 8, description: "中期对话" },
    { sessionId: "test-session-3", rounds: 15, description: "深度对话" },
  ];

  for (const scenario of testCacheScenarios) {
    console.log(`\n测试场景: ${scenario.description} (${scenario.rounds}轮)`);

    // 第一次分析（应该调用AI）
    const firstAnalysis = await aiServices.analyzeCandidateType(
      testDialogues["正常型"].slice(0, scenario.rounds),
      scenario.sessionId
    );
    console.log(`首次分析: ${firstAnalysis.type} (置信度: ${firstAnalysis.confidence})`);

    // 第二次分析（应该命中缓存）
    const secondAnalysis = await aiServices.analyzeCandidateType(
      testDialogues["正常型"].slice(0, scenario.rounds),
      scenario.sessionId
    );
    console.log(`缓存命中: ${secondAnalysis.source === 'cache' ? '✅' : '❌'}`);

    // 增加轮次后分析（可能重新分析）
    if (scenario.rounds < 15) {
      const extendedAnalysis = await aiServices.analyzeCandidateType(
        testDialogues["正常型"].slice(0, scenario.rounds + 5),
        scenario.sessionId
      );
      console.log(`轮次增加后: ${extendedAnalysis.type} (是否重新分析: ${extendedAnalysis.source !== 'cache' ? '是' : '否'})`);
    }
  }

  // 测试缓存清理
  console.log("\n🧹 测试缓存清理功能...");
  await aiServices.cleanupExpiredCache();
  console.log("缓存清理完成");

  console.log("\n🎉 双模型架构测试完成！");
  console.log("\n📋 缓存策略总结:");
  console.log("- 统一缓存时间: 14天");
  console.log("- 重新分析条件: 置信度<60% 或 每10轮");
  console.log("- 存储方式: 文件缓存 (不占用数据库)");
  console.log("- 预期缓存命中率: 85%+");
}

// 运行测试
if (require.main === module) {
  testDualModelArchitecture().catch(console.error);
}

module.exports = { testDualModelArchitecture };
