/**
 * 双模型架构测试脚本
 * 
 * 测试候选人类型分析和费用监控功能
 */

const AIServices = require('./ai-recruitment-assistant/core/数据管理/ai-services');
const UserManager = require('./ai-recruitment-assistant/core/业务服务/user-manager');

// 模拟配置
const testConfig = {
  deepseekEndpoint: "https://api.deepseek.com",
  deepseekApiKey: "test-key",
  qwenEndpoint: "https://dashscope.aliyuncs.com",
  qwenApiKey: "test-key",
  maxTokens: 2000,
  temperature: 0.7,
  timeout: 30000,
  dailyCostLimit: 50, // 50元/天限制
};

// 模拟对话数据
const testDialogues = {
  "挤牙膏型": [
    { role: "assistant", content: "您好，我是AI领域的猎头Katrina，专注于AI算法职位。" },
    { role: "user", content: "你好" },
    { role: "assistant", content: "您考虑看看新机会吗？优质的职位还挺多的。" },
    { role: "user", content: "嗯" },
    { role: "assistant", content: "好的，我们当前合作了很多公司，包含头部大厂、中型公司、创业型团队、国央企等等，职位也比较多。所以得麻烦您，告知我您的技术栈或技术方向、当前所在公司、当前的职级、期望的薪资？这样可以更准确的给您推荐合适的职位。" },
    { role: "user", content: "算法的有吗" },
  ],
  
  "疯狂咨询型": [
    { role: "assistant", content: "您好，我是AI领域的猎头Katrina，专注于AI算法职位。" },
    { role: "user", content: "你好" },
    { role: "assistant", content: "您考虑看看新机会吗？优质的职位还挺多的。" },
    { role: "user", content: "你有什么职位推荐吗" },
    { role: "assistant", content: "好的，我们当前合作了很多公司，包含头部大厂、中型公司、创业型团队、国央企等等，职位也比较多。所以得麻烦您，告知我您的技术栈或技术方向、当前所在公司、当前的职级、期望的薪资？这样可以更准确的给您推荐合适的职位。" },
    { role: "user", content: "你那边具体合作了什么公司呢？" },
  ],

  "质疑/不信任型": [
    { role: "assistant", content: "您好，我是AI领域的猎头Katrina，专注于AI算法职位。" },
    { role: "user", content: "你好" },
    { role: "assistant", content: "您考虑看看新机会吗？优质的职位还挺多的。" },
    { role: "user", content: "你有什么职位推荐吗" },
    { role: "assistant", content: "好的，我们当前合作了很多公司，包含头部大厂、中型公司、创业型团队、国央企等等，职位也比较多。所以得麻烦您，告知我您的技术栈或技术方向、当前所在公司、当前的职级、期望的薪资？这样可以更准确的给您推荐合适的职位。" },
    { role: "user", content: "你那边具体合作了什么公司呢？我在腾讯。" },
    { role: "assistant", content: "公司真的很多，国内知名的科技公司，超过一半都合作了。还得拜托您提供一下您的技术栈或技术方向、当前的职级、和期望的薪资？" },
    { role: "user", content: "公司1有职位吗？他们不是在裁员吗。" },
  ]
};

async function testDualModelArchitecture() {
  console.log("🚀 开始测试双模型架构...\n");

  // 初始化AI服务
  const aiServices = new AIServices(testConfig);
  await aiServices.initialize();

  console.log("📊 初始监控状态:");
  console.log(JSON.stringify(aiServices.getMonitoringStatus(), null, 2));
  console.log("\n" + "=".repeat(50) + "\n");

  // 测试不同类型的候选人分析
  for (const [type, dialogue] of Object.entries(testDialogues)) {
    console.log(`🔍 测试候选人类型: ${type}`);
    
    try {
      // 分析候选人类型
      const analysis = await aiServices.analyzeCandidateType(
        dialogue,
        `test-session-${type}`,
        false
      );
      
      console.log(`✅ 分析结果:`, analysis);
      
      // 测试缓存命中
      const cachedAnalysis = await aiServices.analyzeCandidateType(
        dialogue,
        `test-session-${type}`,
        false
      );
      
      console.log(`💾 缓存测试: ${cachedAnalysis.source === 'cache' ? '命中' : '未命中'}`);
      
    } catch (error) {
      console.error(`❌ 测试失败:`, error.message);
    }
    
    console.log("\n" + "-".repeat(30) + "\n");
  }

  // 测试费用监控
  console.log("💰 测试费用监控...");
  
  // 模拟大量调用
  for (let i = 0; i < 10; i++) {
    aiServices.updateCostTracking('qwen', 200, 50);
    aiServices.updateCostTracking('deepseek', 200, 100);
  }
  
  console.log("📈 费用监控状态:");
  const monitoringStatus = aiServices.getMonitoringStatus();
  console.log(`当前费用: ${monitoringStatus.costMonitor.currentDailyCost.toFixed(4)}元`);
  console.log(`使用率: ${(monitoringStatus.costMonitor.usageRatio * 100).toFixed(1)}%`);
  console.log(`是否被阻止: ${monitoringStatus.costMonitor.isBlocked ? '是' : '否'}`);
  
  console.log("\n" + "=".repeat(50) + "\n");

  // 测试模型健康检查
  console.log("🏥 测试模型健康检查...");
  
  const qwenHealth = await aiServices.checkModelHealth('qwen');
  const deepseekHealth = await aiServices.checkModelHealth('deepseek');
  
  console.log(`Qwen模型健康: ${qwenHealth ? '✅' : '❌'}`);
  console.log(`DeepSeek模型健康: ${deepseekHealth ? '✅' : '❌'}`);

  // 最终统计
  console.log("\n📊 最终统计信息:");
  const finalStats = aiServices.getStats();
  console.log(`总请求数: ${finalStats.totalRequests}`);
  console.log(`Qwen调用数: ${finalStats.qwenCalls}`);
  console.log(`DeepSeek调用数: ${finalStats.deepseekCalls}`);
  console.log(`缓存命中数: ${finalStats.cacheHits}`);
  
  const cacheHitRate = finalStats.cacheHits / (finalStats.qwenCalls + finalStats.cacheHits) || 0;
  console.log(`缓存命中率: ${(cacheHitRate * 100).toFixed(1)}%`);

  console.log("\n🎉 双模型架构测试完成！");
}

// 运行测试
if (require.main === module) {
  testDualModelArchitecture().catch(console.error);
}

module.exports = { testDualModelArchitecture };
