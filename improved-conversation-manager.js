/**
 * 基于CALM论文的改进对话管理器
 * 实现 User-Thought1-Action-Observation-Thought2-Response 结构
 */

class ImprovedConversationManager {
  constructor(aiServices, database) {
    this.aiServices = aiServices;
    this.database = database;
    
    // 改进的缓存策略：基于对话内容而非会话ID
    this.contentBasedCache = new Map();
    this.maxCacheSize = 1000;
    
    // 对话状态跟踪
    this.dialogueStates = new Map();
  }

  /**
   * 处理用户消息 - 实现CALM的CRA结构
   */
  async processMessage(sessionId, userMessage, conversationHistory) {
    console.log(`\n[${new Date().toLocaleTimeString()}] 👤 候选人: ${userMessage}`);
    console.log(`🧠 启动CALM-inspired推理流程...`);
    
    try {
      // Step 1: Thought1 - 分析用户意图和对话状态
      const thought1 = await this.analyzeUserIntent(userMessage, conversationHistory);
      console.log(`💭 Thought1: ${thought1.reasoning}`);
      
      // Step 2: Action - 决定是否需要API调用
      let action = null;
      let observation = null;
      
      if (thought1.needsAnalysis) {
        console.log(`🔍 Action: 调用候选人类型分析API`);
        
        // 使用内容哈希而非会话ID进行缓存
        const contentHash = this.generateContentHash(conversationHistory);
        const cachedResult = this.contentBasedCache.get(contentHash);
        
        if (cachedResult) {
          console.log(`🎯 缓存命中 (基于对话内容)`);
          observation = cachedResult;
        } else {
          console.log(`🌐 真实API调用 (新对话内容)`);
          const analysisStart = Date.now();
          observation = await this.aiServices.analyzeCandidateType(conversationHistory, sessionId);
          const analysisTime = Date.now() - analysisStart;
          
          console.log(`✅ API调用完成 (耗时: ${analysisTime}ms)`);
          
          // 缓存结果
          this.contentBasedCache.set(contentHash, observation);
          this.cleanupCache();
        }
        
        action = "analyze_candidate_type";
      } else {
        console.log(`💬 Action: 直接回复 (无需API调用)`);
        observation = { type: "direct_response", confidence: 1.0 };
      }
      
      console.log(`📊 Observation: ${JSON.stringify(observation, null, 2)}`);
      
      // Step 3: Thought2 - 基于观察结果决定回复策略
      const thought2 = await this.planResponse(thought1, observation, userMessage, conversationHistory);
      console.log(`💭 Thought2: ${thought2.reasoning}`);
      
      // Step 4: Response - 生成最终回复
      const response = await this.generateResponse(thought2, userMessage, conversationHistory);
      console.log(`[${new Date().toLocaleTimeString()}] 🤖 AI领域的猎头Katrina: ${response.content}`);
      console.log(`📝 回复策略: ${response.strategy}`);
      
      // 更新对话状态
      this.updateDialogueState(sessionId, userMessage, response, observation);
      
      return {
        success: true,
        response: response,
        metadata: {
          thought1: thought1,
          action: action,
          observation: observation,
          thought2: thought2,
          processingTime: Date.now() - Date.now()
        }
      };
      
    } catch (error) {
      console.error(`❌ 对话处理失败:`, error);
      return {
        success: false,
        response: {
          content: "抱歉，系统出现了问题，请稍后重试。",
          strategy: "error_fallback"
        },
        error: error.message
      };
    }
  }

  /**
   * Thought1: 分析用户意图和对话状态
   */
  async analyzeUserIntent(userMessage, conversationHistory) {
    const userMessageLower = userMessage.toLowerCase().trim();
    const messageLength = userMessage.length;
    const conversationLength = conversationHistory.length;
    
    // 分析对话模式
    const userMessages = conversationHistory.filter(msg => msg.role === 'user');
    const avgMessageLength = userMessages.reduce((sum, msg) => sum + msg.content.length, 0) / userMessages.length || 0;
    
    // 检测信息提供情况
    const hasCompanyInfo = /腾讯|阿里|字节|华为|百度|美团|滴滴|小米/.test(userMessage);
    const hasTechInfo = /算法|机器学习|深度学习|AI|人工智能|推荐|NLP|CV|计算机视觉/.test(userMessage);
    const hasSalaryInfo = /\d+[kKwW万千]|薪资|工资|待遇/.test(userMessage);
    const hasJobLevelInfo = /高级|资深|专家|总监|经理|工程师|架构师/.test(userMessage);
    
    let reasoning = "";
    let needsAnalysis = false;
    let intent = "unknown";
    
    if (conversationLength <= 2) {
      // 首次交互
      if (userMessageLower.includes('你好') || userMessageLower.includes('hi')) {
        reasoning = "用户进行问候，这是对话开始的标准流程";
        intent = "greeting";
        needsAnalysis = true;
      } else if (userMessageLower.includes('职位') || userMessageLower.includes('工作')) {
        reasoning = "用户直接询问职位信息，显示明确的求职意图";
        intent = "job_inquiry";
        needsAnalysis = true;
      } else if (messageLength <= 3) {
        reasoning = "用户回复极简短，可能是挤牙膏型候选人";
        intent = "minimal_response";
        needsAnalysis = true;
      } else {
        reasoning = "用户提供了较详细的信息，可能是主动型候选人";
        intent = "detailed_response";
        needsAnalysis = true;
      }
    } else {
      // 后续交互
      if (hasCompanyInfo || hasTechInfo || hasSalaryInfo || hasJobLevelInfo) {
        reasoning = "用户提供了关键职业信息，需要分析信息完整性";
        intent = "information_providing";
        needsAnalysis = true;
      } else if (userMessageLower.includes('需要') || userMessageLower.includes('什么信息')) {
        reasoning = "用户询问需要提供什么信息，显示配合意愿";
        intent = "information_inquiry";
        needsAnalysis = false; // 直接回复即可
      } else if (avgMessageLength < 5) {
        reasoning = "用户持续简短回复，确认挤牙膏型特征";
        intent = "continued_minimal";
        needsAnalysis = true;
      } else {
        reasoning = "用户回复内容需要进一步分析以确定最佳回复策略";
        intent = "general_response";
        needsAnalysis = true;
      }
    }
    
    return {
      reasoning,
      needsAnalysis,
      intent,
      messageAnalysis: {
        length: messageLength,
        hasCompanyInfo,
        hasTechInfo,
        hasSalaryInfo,
        hasJobLevelInfo
      }
    };
  }

  /**
   * Thought2: 基于观察结果规划回复策略
   */
  async planResponse(thought1, observation, userMessage, conversationHistory) {
    let reasoning = "";
    let strategy = "";
    let responseType = "";
    
    const userMessages = conversationHistory.filter(msg => msg.role === 'user');
    const roundCount = userMessages.length;
    
    if (observation.type === "direct_response") {
      // 无需API调用的情况
      if (thought1.intent === "information_inquiry") {
        reasoning = "用户询问需要什么信息，直接提供信息需求清单";
        strategy = "provide_information_requirements";
        responseType = "informational";
      } else {
        reasoning = "根据对话上下文提供通用回复";
        strategy = "general_guidance";
        responseType = "general";
      }
    } else {
      // 基于候选人类型分析结果
      const candidateType = observation.type;
      const confidence = observation.confidence;
      
      if (candidateType === "挤牙膏型") {
        if (roundCount === 1) {
          reasoning = "首次识别为挤牙膏型，使用引导式提问激发回应";
          strategy = "gentle_engagement";
          responseType = "engaging_question";
        } else if (roundCount === 2) {
          reasoning = "持续挤牙膏型行为，提供具体信息需求";
          strategy = "specific_information_request";
          responseType = "structured_inquiry";
        } else {
          reasoning = "多轮挤牙膏型，需要更直接的信息收集";
          strategy = "direct_information_collection";
          responseType = "direct_request";
        }
      } else if (candidateType === "主动型") {
        reasoning = "候选人主动提供信息，给予积极反馈并深入了解";
        strategy = "positive_reinforcement";
        responseType = "encouraging_followup";
      } else {
        reasoning = "候选人类型不明确，使用平衡的探索性回复";
        strategy = "exploratory_response";
        responseType = "balanced_inquiry";
      }
      
      // 根据信息完整性调整策略
      if (thought1.messageAnalysis.hasCompanyInfo && thought1.messageAnalysis.hasTechInfo) {
        reasoning += " 候选人已提供公司和技术信息，可以进行职位匹配";
        strategy = "job_matching";
        responseType = "matching_response";
      }
    }
    
    return {
      reasoning,
      strategy,
      responseType,
      candidateType: observation.type,
      confidence: observation.confidence
    };
  }

  /**
   * 生成最终回复
   */
  async generateResponse(thought2, userMessage, conversationHistory) {
    const { strategy, responseType, candidateType } = thought2;
    let content = "";
    
    // 根据策略生成回复
    switch (strategy) {
      case "gentle_engagement":
        content = "您考虑看看新机会吗？优质的职位还挺多的。";
        break;
        
      case "specific_information_request":
        content = "我们当前合作了很多公司，包含头部大厂、中型公司、创业型团队、国央企等等，职位也比较多，麻烦您告知一下您的信息点，便于我能够给您来推荐合适的职位。";
        break;
        
      case "direct_information_collection":
        content = "得拜托您告知我，您的技术栈或技术方向、当前所在公司、当前的职级、期望的薪资？";
        break;
        
      case "provide_information_requirements":
        content = "得拜托您告知我，您的技术栈或技术方向、当前所在公司、当前的职级、期望的薪资？";
        break;
        
      case "job_matching":
        // 基于用户信息进行职位匹配回复
        const userMessageLower = userMessage.toLowerCase();
        if (userMessageLower.includes('华为')) {
          content = "了解，您在华为的背景很不错。基于您的经验，我这边有几个不错的职位推荐。您对哪个技术方向更感兴趣呢？";
        } else if (userMessageLower.includes('腾讯')) {
          content = "很好！腾讯的背景确实很有竞争力。基于您的经验，我这边有几个不错的职位推荐。您对哪个方向更感兴趣：深度学习、NLP、还是计算机视觉？";
        } else {
          content = "很好！基于您提供的信息，我这边有几个不错的职位推荐。您对哪个技术方向更感兴趣呢？";
        }
        break;
        
      default:
        content = "好的，我了解了。还有其他信息可以分享吗？这样我能为您推荐更合适的职位。";
    }
    
    return {
      content,
      strategy,
      responseType,
      candidateType
    };
  }

  /**
   * 生成基于对话内容的哈希
   */
  generateContentHash(conversationHistory) {
    const content = conversationHistory
      .map(msg => `${msg.role}:${msg.content}`)
      .join('|');
    
    // 简单哈希函数
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return hash.toString();
  }

  /**
   * 清理缓存
   */
  cleanupCache() {
    if (this.contentBasedCache.size > this.maxCacheSize) {
      const entries = Array.from(this.contentBasedCache.entries());
      const toDelete = entries.slice(0, Math.floor(this.maxCacheSize * 0.2));
      toDelete.forEach(([key]) => this.contentBasedCache.delete(key));
    }
  }

  /**
   * 更新对话状态
   */
  updateDialogueState(sessionId, userMessage, response, observation) {
    if (!this.dialogueStates.has(sessionId)) {
      this.dialogueStates.set(sessionId, {
        candidateType: null,
        informationCollected: {},
        conversationStage: 'initial'
      });
    }
    
    const state = this.dialogueStates.get(sessionId);
    
    // 更新候选人类型
    if (observation && observation.type) {
      state.candidateType = observation.type;
    }
    
    // 提取信息
    const userMessageLower = userMessage.toLowerCase();
    if (/腾讯|阿里|字节|华为|百度/.test(userMessage)) {
      state.informationCollected.company = userMessage.match(/(腾讯|阿里|字节|华为|百度)/)[1];
    }
    if (/算法|机器学习|深度学习|AI|推荐|NLP|CV/.test(userMessage)) {
      state.informationCollected.techStack = userMessage;
    }
    if (/\d+[kKwW万千]/.test(userMessage)) {
      state.informationCollected.salary = userMessage.match(/\d+[kKwW万千]/)[0];
    }
    
    // 更新对话阶段
    const infoCount = Object.keys(state.informationCollected).length;
    if (infoCount >= 3) {
      state.conversationStage = 'job_matching';
    } else if (infoCount >= 1) {
      state.conversationStage = 'information_gathering';
    }
    
    this.dialogueStates.set(sessionId, state);
  }

  /**
   * 获取统计信息
   */
  getStats() {
    return {
      cacheSize: this.contentBasedCache.size,
      activeSessions: this.dialogueStates.size,
      aiServiceStats: this.aiServices.getStats()
    };
  }
}

module.exports = ImprovedConversationManager;
