-- 候选人类型缓存表（支持14天长期缓存）
-- 用于存储候选人类型分析结果，支持跨会话持久化

CREATE TABLE IF NOT EXISTS candidate_type_cache (
    id SERIAL PRIMARY KEY,
    session_id VARCHAR(255) NOT NULL UNIQUE,
    analysis_result JSONB NOT NULL,
    rounds INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_candidate_cache_session_id ON candidate_type_cache(session_id);
CREATE INDEX IF NOT EXISTS idx_candidate_cache_created_at ON candidate_type_cache(created_at);
CREATE INDEX IF NOT EXISTS idx_candidate_cache_rounds ON candidate_type_cache(rounds);

-- 创建自动更新时间戳的触发器
CREATE OR REPLACE FUNCTION update_candidate_cache_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_candidate_cache_updated_at
    BEFORE UPDATE ON candidate_type_cache
    FOR EACH ROW
    EXECUTE FUNCTION update_candidate_cache_updated_at();

-- 创建自动清理过期缓存的函数
CREATE OR REPLACE FUNCTION cleanup_expired_candidate_cache()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- 删除超过14天的缓存记录
    DELETE FROM candidate_type_cache 
    WHERE created_at < NOW() - INTERVAL '14 days';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- 记录清理日志
    INSERT INTO system_logs (log_type, message, created_at)
    VALUES ('cache_cleanup', 
            'Cleaned up ' || deleted_count || ' expired candidate type cache records',
            NOW());
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- 创建定时清理任务（如果支持pg_cron扩展）
-- SELECT cron.schedule('cleanup-candidate-cache', '0 2 * * *', 'SELECT cleanup_expired_candidate_cache();');

-- 示例数据结构
-- analysis_result JSONB 字段的结构示例：
/*
{
  "type": "挤牙膏型",
  "confidence": 0.8,
  "evidence": ["短回复比例高", "平均消息长度短"],
  "strategy": "特殊处理",
  "riskLevel": "medium",
  "timestamp": 1640995200000,
  "behaviorPattern": {
    "avgMessageLength": 3.5,
    "questionRatio": 0.1,
    "shortReplyRatio": 0.8,
    "infoProvisionRatio": 0.2,
    "doubtExpressions": [],
    "avoidanceBehaviors": []
  }
}
*/

-- 查询统计信息的视图
CREATE OR REPLACE VIEW candidate_cache_stats AS
SELECT 
    COUNT(*) as total_cached,
    COUNT(CASE WHEN created_at > NOW() - INTERVAL '1 hour' THEN 1 END) as cached_last_hour,
    COUNT(CASE WHEN created_at > NOW() - INTERVAL '1 day' THEN 1 END) as cached_last_day,
    COUNT(CASE WHEN created_at > NOW() - INTERVAL '7 days' THEN 1 END) as cached_last_week,
    AVG(rounds) as avg_rounds,
    analysis_result->>'type' as candidate_type,
    COUNT(*) as type_count
FROM candidate_type_cache
GROUP BY analysis_result->>'type'
ORDER BY type_count DESC;

-- 性能优化建议
COMMENT ON TABLE candidate_type_cache IS '候选人类型缓存表，支持14天长期缓存，用于减少AI模型调用成本';
COMMENT ON COLUMN candidate_type_cache.session_id IS '会话ID，关联chat_sessions表';
COMMENT ON COLUMN candidate_type_cache.analysis_result IS 'AI分析结果的JSON数据，包含类型、置信度、证据等';
COMMENT ON COLUMN candidate_type_cache.rounds IS '分析时的对话轮次，用于确定缓存过期策略';
COMMENT ON COLUMN candidate_type_cache.created_at IS '缓存创建时间，用于过期清理';
COMMENT ON COLUMN candidate_type_cache.updated_at IS '缓存更新时间，用于跟踪最后修改';
