/**
 * 真实API调用测试
 * 直接调用DeepSeek V3和Qwen-Turbo API进行测试
 */

require("dotenv").config({ path: "../.env" });
const axios = require("axios");

class RealAPITest {
  constructor() {
    // DeepSeek V3 配置
    this.deepseekConfig = {
      apiKey: process.env.DEEPSEEK_API_KEY,
      endpoint: process.env.DEEPSEEK_ENDPOINT,
      model: process.env.DEEPSEEK_MODEL || "deepseek-chat"
    };

    // Qwen-Turbo 配置
    this.qwenConfig = {
      apiKey: process.env.QWEN_API_KEY,
      endpoint: process.env.QWEN_ENDPOINT,
      model: process.env.QWEN_MODEL || "qwen-turbo"
    };

    this.testResults = {
      deepseek: [],
      qwen: [],
      startTime: new Date()
    };
  }

  async runAllTests() {
    console.log('🚀 开始真实API调用测试\n');
    console.log(`📅 测试时间: ${this.testResults.startTime.toISOString()}`);
    console.log(`🔑 DeepSeek API Key: ${this.deepseekConfig.apiKey ? this.deepseekConfig.apiKey.substring(0, 10) + '...' : '未配置'}`);
    console.log(`🔑 Qwen API Key: ${this.qwenConfig.apiKey ? this.qwenConfig.apiKey.substring(0, 10) + '...' : '未配置'}\n`);

    // 测试场景
    const testScenarios = [
      {
        name: "用户意图分析",
        userMessage: "我想找工作",
        systemPrompt: "你是一个招聘助手，请分析用户的意图和需求。用户说：",
        expectedFunction: "分析求职意图"
      },
      {
        name: "信息提取",
        userMessage: "我在腾讯做推荐算法，P7级别，期望年薪150万",
        systemPrompt: "请从用户消息中提取关键信息：公司、技术方向、职级、薪资。用户说：",
        expectedFunction: "提取结构化信息"
      },
      {
        name: "推荐生成",
        userMessage: "有没有大厂的机会？",
        systemPrompt: "用户询问工作机会，请生成个性化的回复和推荐建议。用户说：",
        expectedFunction: "生成推荐回复"
      }
    ];

    // 测试DeepSeek V3
    console.log('🤖 测试DeepSeek V3 API...\n');
    for (const scenario of testScenarios) {
      await this.testDeepSeekAPI(scenario);
      await this.sleep(1000); // API调用间隔
    }

    // 测试Qwen-Turbo
    console.log('\n🤖 测试Qwen-Turbo API...\n');
    for (const scenario of testScenarios) {
      await this.testQwenAPI(scenario);
      await this.sleep(1000); // API调用间隔
    }

    // 生成测试报告
    this.generateTestReport();
  }

  async testDeepSeekAPI(scenario) {
    console.log(`📝 DeepSeek测试: ${scenario.name}`);
    console.log(`👤 用户消息: "${scenario.userMessage}"`);
    
    const startTime = Date.now();
    
    try {
      const response = await axios.post(
        this.deepseekConfig.endpoint,
        {
          model: this.deepseekConfig.model,
          messages: [
            {
              role: "system",
              content: scenario.systemPrompt
            },
            {
              role: "user", 
              content: scenario.userMessage
            }
          ],
          max_tokens: 500,
          temperature: 0.7,
          stream: false
        },
        {
          headers: {
            'Authorization': `Bearer ${this.deepseekConfig.apiKey}`,
            'Content-Type': 'application/json'
          },
          timeout: 30000
        }
      );

      const duration = Date.now() - startTime;
      const aiResponse = response.data.choices[0].message.content;
      
      console.log(`🤖 DeepSeek回复: ${aiResponse.substring(0, 200)}${aiResponse.length > 200 ? '...' : ''}`);
      console.log(`⏱️ 响应时间: ${duration}ms`);
      console.log(`💰 Token使用: ${response.data.usage?.total_tokens || 'N/A'}\n`);

      this.testResults.deepseek.push({
        scenario: scenario.name,
        success: true,
        duration: duration,
        response: aiResponse,
        tokens: response.data.usage?.total_tokens || 0,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      const duration = Date.now() - startTime;
      console.log(`❌ DeepSeek调用失败: ${error.message}`);
      console.log(`⏱️ 失败时间: ${duration}ms\n`);

      this.testResults.deepseek.push({
        scenario: scenario.name,
        success: false,
        duration: duration,
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  async testQwenAPI(scenario) {
    console.log(`📝 Qwen测试: ${scenario.name}`);
    console.log(`👤 用户消息: "${scenario.userMessage}"`);
    
    const startTime = Date.now();
    
    try {
      const response = await axios.post(
        this.qwenConfig.endpoint,
        {
          model: this.qwenConfig.model,
          input: {
            messages: [
              {
                role: "system",
                content: scenario.systemPrompt
              },
              {
                role: "user",
                content: scenario.userMessage
              }
            ]
          },
          parameters: {
            max_tokens: 500,
            temperature: 0.7,
            result_format: "message"
          }
        },
        {
          headers: {
            'Authorization': `Bearer ${this.qwenConfig.apiKey}`,
            'Content-Type': 'application/json',
            'X-DashScope-SSE': 'disable'
          },
          timeout: 30000
        }
      );

      const duration = Date.now() - startTime;
      const aiResponse = response.data.output?.choices?.[0]?.message?.content || 
                        response.data.output?.text || 
                        'No response content';
      
      console.log(`🤖 Qwen回复: ${aiResponse.substring(0, 200)}${aiResponse.length > 200 ? '...' : ''}`);
      console.log(`⏱️ 响应时间: ${duration}ms`);
      console.log(`💰 Token使用: ${response.data.usage?.total_tokens || 'N/A'}\n`);

      this.testResults.qwen.push({
        scenario: scenario.name,
        success: true,
        duration: duration,
        response: aiResponse,
        tokens: response.data.usage?.total_tokens || 0,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      const duration = Date.now() - startTime;
      console.log(`❌ Qwen调用失败: ${error.message}`);
      console.log(`⏱️ 失败时间: ${duration}ms\n`);

      this.testResults.qwen.push({
        scenario: scenario.name,
        success: false,
        duration: duration,
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  generateTestReport() {
    console.log('\n📊 ==================== API测试报告 ====================');
    
    const totalTests = this.testResults.deepseek.length + this.testResults.qwen.length;
    const successfulTests = [
      ...this.testResults.deepseek.filter(t => t.success),
      ...this.testResults.qwen.filter(t => t.success)
    ];
    
    console.log(`🧪 总测试数: ${totalTests}`);
    console.log(`✅ 成功调用: ${successfulTests.length}`);
    console.log(`❌ 失败调用: ${totalTests - successfulTests.length}`);
    console.log(`📈 成功率: ${((successfulTests.length / totalTests) * 100).toFixed(1)}%`);

    // DeepSeek V3 统计
    const deepseekSuccess = this.testResults.deepseek.filter(t => t.success);
    const deepseekAvgTime = deepseekSuccess.length > 0 ? 
      deepseekSuccess.reduce((sum, t) => sum + t.duration, 0) / deepseekSuccess.length : 0;
    const deepseekTotalTokens = deepseekSuccess.reduce((sum, t) => sum + (t.tokens || 0), 0);

    console.log(`\n🤖 DeepSeek V3 统计:`);
    console.log(`   成功率: ${deepseekSuccess.length}/${this.testResults.deepseek.length}`);
    console.log(`   平均响应时间: ${deepseekAvgTime.toFixed(0)}ms`);
    console.log(`   总Token消耗: ${deepseekTotalTokens}`);

    // Qwen-Turbo 统计
    const qwenSuccess = this.testResults.qwen.filter(t => t.success);
    const qwenAvgTime = qwenSuccess.length > 0 ? 
      qwenSuccess.reduce((sum, t) => sum + t.duration, 0) / qwenSuccess.length : 0;
    const qwenTotalTokens = qwenSuccess.reduce((sum, t) => sum + (t.tokens || 0), 0);

    console.log(`\n🤖 Qwen-Turbo 统计:`);
    console.log(`   成功率: ${qwenSuccess.length}/${this.testResults.qwen.length}`);
    console.log(`   平均响应时间: ${qwenAvgTime.toFixed(0)}ms`);
    console.log(`   总Token消耗: ${qwenTotalTokens}`);

    // 详细结果
    console.log(`\n📝 详细测试结果:`);
    
    console.log(`\nDeepSeek V3:`);
    this.testResults.deepseek.forEach((result, i) => {
      const status = result.success ? '✅' : '❌';
      console.log(`  ${i+1}. ${status} ${result.scenario} (${result.duration}ms)`);
      if (!result.success) {
        console.log(`     错误: ${result.error}`);
      }
    });

    console.log(`\nQwen-Turbo:`);
    this.testResults.qwen.forEach((result, i) => {
      const status = result.success ? '✅' : '❌';
      console.log(`  ${i+1}. ${status} ${result.scenario} (${result.duration}ms)`);
      if (!result.success) {
        console.log(`     错误: ${result.error}`);
      }
    });

    // 测试结论
    console.log(`\n🎯 测试结论:`);
    if (successfulTests.length === totalTests) {
      console.log('✅ 所有API调用成功，双模型系统运行正常');
    } else if (successfulTests.length > totalTests / 2) {
      console.log('⚠️ 部分API调用失败，需要检查失败原因');
    } else {
      console.log('❌ 大部分API调用失败，系统存在严重问题');
    }

    const endTime = new Date();
    const totalDuration = endTime.getTime() - this.testResults.startTime.getTime();
    console.log(`\n⏱️ 总测试时间: ${totalDuration}ms`);
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 运行测试
async function runAPITest() {
  const test = new RealAPITest();
  
  try {
    await test.runAllTests();
  } catch (error) {
    console.error('❌ API测试执行失败:', error);
  }
}

if (require.main === module) {
  runAPITest();
}

module.exports = RealAPITest;
