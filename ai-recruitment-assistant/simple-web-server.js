/**
 * 简单的AI招聘助手测试服务器
 * 用于演示前端界面，使用模拟数据
 */

const express = require('express');
const cors = require('cors');
const path = require('path');

const app = express();
const PORT = 3000;

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, '../public')));

// 模拟会话存储
let sessions = new Map();
let messageCount = 0;

// 模拟AI回复
const mockReplies = [
  "您好！我是AI招聘助手Katrina。我可以帮您分析职位匹配度、推荐合适的工作机会。请告诉我您的技术方向和求职需求。",
  "您考虑看看新机会吗？优质的职位还挺多的。",
  "好的，我了解您在寻找工作机会。能告诉我您的技术方向吗？比如前端、后端、算法、数据等。",
  "我刚刚快速的查询了一下，先给你推荐几个职位，你看看有没有感兴趣的。\n\n1. 🏢 高德地图 - 个性化推荐算法高阶\n💼 P7,P8,P9 📍 地点待定\n🎯 负责推荐算法、机器学习相关工作\n⭐ 匹配度：85%\n回复\"详情1\"查看完整职位信息\n\n2. 🏢 饿了么 - 算法专家-用户/广告/推荐/运筹定价\n💼 对标P6-P8 📍 地点待定\n🎯 负责推荐算法、机器学习相关工作\n⭐ 匹配度：82%\n回复\"详情2\"查看完整职位信息\n\n3. 🏢 创新工场 - 推荐算法工程师\n💼 职级面议 📍 地点待定\n🎯 负责推荐算法、机器学习相关工作\n⭐ 匹配度：78%\n回复\"详情3\"查看完整职位信息\n\n您手头上有简历吗？麻烦您发一下给我，这样能够给您匹配更加精准的职位。",
  "根据您的背景，我建议您重点关注以下几个方向：\n\n1. **头部大厂**：腾讯、阿里、字节等，技术挑战大，成长空间足\n2. **中型公司**：美团、滴滴等，平台稳定，发展机会多\n3. **创业公司**：技术栈新，责任范围广，股权激励\n\n您比较倾向于哪种类型的公司呢？",
  "好的，我会为您持续关注合适的职位机会。如果有新的匹配职位，我会及时通知您。\n\n同时，您也可以点击左下角的邮箱按钮，分享您的个人邮箱，我们可以更好的保持联系，定期的给您推送合适的职位。"
];

// 获取开场白
app.get('/api/init', (req, res) => {
  const sessionId = `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  
  // 存储会话
  sessions.set(sessionId, {
    id: sessionId,
    messages: [],
    createdAt: new Date(),
    candidateType: null,
    messageCount: 0,
  });
  
  res.json({
    success: true,
    sessionId: sessionId,
    message: {
      type: 'welcome',
      content: mockReplies[0],
      timestamp: new Date().toISOString(),
    },
  });
});

// 处理聊天消息
app.post('/api/chat', async (req, res) => {
  try {
    const { sessionId, message } = req.body;
    
    if (!sessionId || !message) {
      return res.status(400).json({
        success: false,
        error: '缺少必要参数',
      });
    }
    
    const startTime = Date.now();
    
    // 获取或创建会话
    let session = sessions.get(sessionId);
    if (!session) {
      session = {
        id: sessionId,
        messages: [],
        createdAt: new Date(),
        candidateType: null,
        messageCount: 0,
      };
      sessions.set(sessionId, session);
    }
    
    // 添加用户消息到历史
    session.messages.push({
      type: 'user',
      content: message,
      timestamp: new Date().toISOString(),
    });
    
    session.messageCount++;
    messageCount++;
    
    // 模拟处理延迟
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
    
    // 选择回复
    let replyIndex = Math.min(session.messageCount, mockReplies.length - 1);
    
    // 根据消息内容选择特定回复
    const lowerMessage = message.toLowerCase();
    if (lowerMessage.includes('推荐算法') || lowerMessage.includes('算法')) {
      replyIndex = 3; // 职位推荐回复
    } else if (lowerMessage.includes('大厂') || lowerMessage.includes('公司')) {
      replyIndex = 4; // 公司类型回复
    }
    
    const aiReply = mockReplies[replyIndex];
    
    // 模拟候选人类型分析
    if (!session.candidateType && session.messageCount >= 2) {
      const types = ['挤牙膏型', '疯狂咨询型', '质疑不信任型'];
      session.candidateType = {
        type: types[Math.floor(Math.random() * types.length)],
        confidence: 0.7 + Math.random() * 0.3,
      };
    }
    
    // 添加助手回复到历史
    session.messages.push({
      type: 'assistant',
      content: aiReply,
      timestamp: new Date().toISOString(),
    });
    
    const processingTime = Date.now() - startTime;
    
    res.json({
      success: true,
      message: {
        type: 'response',
        content: aiReply,
        timestamp: new Date().toISOString(),
        metadata: {
          processingTime: processingTime,
          candidateType: session.candidateType,
          messageCount: session.messageCount,
        },
      },
    });
    
  } catch (error) {
    console.error('❌ 处理消息失败:', error);
    res.status(500).json({
      success: false,
      error: '处理消息失败',
    });
  }
});

// 获取会话统计
app.get('/api/stats/:sessionId', (req, res) => {
  const { sessionId } = req.params;
  const session = sessions.get(sessionId);
  
  if (!session) {
    return res.status(404).json({
      success: false,
      error: '会话不存在',
    });
  }
  
  const stats = {
    sessionId: sessionId,
    messageCount: session.messageCount,
    candidateType: session.candidateType,
    apiCalls: {
      qwen: Math.floor(session.messageCount / 2),
      deepseek: session.messageCount,
      cacheHits: Math.floor(session.messageCount * 0.3),
    },
    cost: session.messageCount * 0.001234, // 模拟费用
    createdAt: session.createdAt,
    lastActivity: session.messages.length > 0 ? 
      session.messages[session.messages.length - 1].timestamp : session.createdAt,
  };
  
  res.json({
    success: true,
    stats: stats,
  });
});

// 健康检查
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    status: 'healthy',
    timestamp: new Date().toISOString(),
    sessions: sessions.size,
    totalMessages: messageCount,
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`\n🌐 AI招聘助手演示服务器启动成功！`);
  console.log(`📱 前端地址: http://localhost:${PORT}`);
  console.log(`🔗 API地址: http://localhost:${PORT}/api`);
  console.log(`\n📋 可用接口:`);
  console.log(`   GET  /api/init - 获取开场白`);
  console.log(`   POST /api/chat - 发送消息`);
  console.log(`   GET  /api/stats/:sessionId - 获取统计`);
  console.log(`   GET  /api/health - 健康检查`);
  console.log(`\n🎯 现在您可以在浏览器中测试AI招聘助手界面了！`);
  console.log(`💡 这是演示版本，使用模拟数据和回复`);
});
