/**
 * 完整系统测试
 * 测试升级后的推荐系统、信息收集、被动推荐检测等功能
 */

// 加载环境变量
require("dotenv").config({ path: "../.env" });

const DatabaseManager = require("./core/数据管理/database-manager");
const PassiveRecommender = require("./core/业务服务/passive-recommender");
const ActiveRecommender = require("./core/业务服务/active-recommender");
const UserManager = require("./core/业务服务/user-manager");
const MappingTables = require("./core/工具库/mapping-tables");
const Utilities = require("./core/工具库/utilities");

class CompleteSystemTest {
  constructor() {
    this.testUserId = 2001; // 固定测试用户ID
    this.testSessionId = "test-session-" + Date.now();
  }

  async initialize() {
    try {
      console.log("🚀 初始化完整系统测试...");

      // 初始化数据库
      const dbConfig = {
        supabaseUrl: process.env.SUPABASE_URL,
        supabaseKey: process.env.SUPABASE_ANON_KEY,
      };

      this.database = new DatabaseManager(dbConfig);
      await this.database.connect();

      // 初始化各个模块
      this.passiveRecommender = new PassiveRecommender(this.database, {});
      await this.passiveRecommender.initialize();

      this.activeRecommender = new ActiveRecommender(this.database, {});
      await this.activeRecommender.initialize();

      this.userManager = new UserManager(this.database, {});
      await this.userManager.initialize(); // 初始化用户管理器

      this.mappingTables = new MappingTables();
      this.mappingTables.database = this.database; // 手动设置database引用
      this.utilities = new Utilities();

      console.log("✅ 系统初始化完成");
    } catch (error) {
      console.error("❌ 初始化失败:", error);
      throw error;
    }
  }

  async runAllTests() {
    try {
      console.log("\n🧪 开始完整系统测试...\n");

      // 测试1: 职级对照查询
      await this.testLevelMapping();

      // 测试2: 被动推荐检测
      await this.testPassiveRecommendationDetection();

      // 测试3: 信息提取工具
      await this.testInformationExtraction();

      // 测试4: 4×4推荐引擎
      await this.test4x4RecommendationEngine();

      // 测试5: 推荐触发条件
      await this.testRecommendationTrigger();

      // 测试6: 完整推荐流程
      await this.testCompleteRecommendationFlow();

      console.log("\n🎉 所有测试完成！");
    } catch (error) {
      console.error("\n❌ 测试失败:", error);
      throw error;
    }
  }

  async testLevelMapping() {
    console.log("📝 测试1: 职级对照查询\n");

    try {
      // 测试P级查询
      console.log("🔸 测试P级职级查询");
      const p7Mapping = await this.mappingTables.queryLevelMapping(
        "P7",
        "阿里巴巴"
      );
      console.log("✅ P7查询结果:", p7Mapping);

      // 测试职级提取
      console.log("\n🔸 测试职级提取");
      const extractedLevels =
        this.mappingTables.extractLevelFromText("我是阿里P7，想找个P8的机会");
      console.log("✅ 提取的职级:", extractedLevels);

      // 测试P级描述
      console.log("\n🔸 测试P级描述");
      const p8Description = this.mappingTables.getPLevelDescription(8);
      console.log("✅ P8描述:", p8Description);
    } catch (error) {
      console.error("❌ 职级对照测试失败:", error);
    }
  }

  async testPassiveRecommendationDetection() {
    console.log("\n📝 测试2: 被动推荐检测\n");

    try {
      const testMessages = [
        "有没有大厂的机会？",
        "推荐一些国企的职位",
        "我想了解外企的情况",
        "创业公司怎么样？",
        "今天天气不错", // 负面测试
      ];

      for (const message of testMessages) {
        console.log(`🔸 测试消息: "${message}"`);
        const analysis =
          this.activeRecommender.analyzePassiveRecommendationRequest(message);
        console.log(`✅ 分析结果:`, {
          isPassive: analysis.isPassiveRecommendation,
          confidence: analysis.confidence.toFixed(2),
          primaryType: analysis.primaryCompanyType?.name || "无",
          strategy: analysis.recommendation?.action || "无",
        });
        console.log("");
      }
    } catch (error) {
      console.error("❌ 被动推荐检测测试失败:", error);
    }
  }

  async testInformationExtraction() {
    console.log("\n📝 测试3: 信息提取工具\n");

    try {
      const testMessage =
        "我在腾讯做推荐算法，P8级别，期望30-40万，想在北京找机会";

      console.log(`🔸 测试消息: "${testMessage}"`);

      // 测试薪资提取
      const salary = this.utilities.extractSalaryFromMessage(testMessage);
      console.log("✅ 提取薪资:", salary);

      // 测试城市提取
      const city = this.utilities.extractCity(testMessage);
      console.log("✅ 提取城市:", city);

      // 测试公司别名
      const aliases = this.utilities.getCompanyAliases("腾讯");
      console.log("✅ 腾讯别名:", aliases);

      // 测试被动推荐检测
      const passiveDetection =
        this.utilities.detectPassiveRecommendationRequest("有没有外企的机会");
      console.log("✅ 被动推荐检测:", passiveDetection);
    } catch (error) {
      console.error("❌ 信息提取测试失败:", error);
    }
  }

  async test4x4RecommendationEngine() {
    console.log("\n📝 测试4: 4×4推荐引擎\n");

    try {
      // 模拟用户档案
      const mockProfile = {
        id: this.testUserId,
        primary_tech_direction_id: 725,
        expected_compensation_min: 250,
        expected_compensation_max: 350,
        candidate_standard_level_min: 7,
        candidate_standard_level_max: 8,
      };

      // 模拟数据库查询
      this.database.getCandidateProfile = async () => mockProfile;
      this.database.searchJobs = async () => [
        {
          id: 1,
          job_title: "推荐算法专家",
          companies: { company_name: "字节跳动", company_type: "头部大厂" },
          salary_min: 300,
          salary_max: 450,
          primary_tech_direction_id: 725,
          job_standard_level_min: 7,
          job_standard_level_max: 9,
          job_description: "负责推荐系统核心算法设计",
          work_location: "北京",
        },
      ];

      // 测试默认推荐
      console.log("🔸 测试默认4×4推荐");
      const defaultRec =
        await this.passiveRecommender.generate4x4Recommendations(
          this.testUserId
        );
      console.log("✅ 默认推荐:", {
        pattern: defaultRec.pattern,
        count: defaultRec.recommendations.length,
      });

      // 测试排除推荐
      console.log("\n🔸 测试排除大厂推荐");
      const excludeRec =
        await this.passiveRecommender.generate4x4Recommendations(
          this.testUserId,
          ["A"]
        );
      console.log("✅ 排除推荐:", {
        pattern: excludeRec.pattern,
        excluded: excludeRec.excludedTypes,
      });

      // 测试被动推荐
      console.log("\n🔸 测试被动推荐（只推国企）");
      const passiveRec =
        await this.passiveRecommender.generate4x4Recommendations(
          this.testUserId,
          [],
          "B"
        );
      console.log("✅ 被动推荐:", {
        pattern: passiveRec.pattern,
        target: passiveRec.targetType,
      });
    } catch (error) {
      console.error("❌ 4×4推荐引擎测试失败:", error);
    }
  }

  async testRecommendationTrigger() {
    console.log("\n📝 测试5: 推荐触发条件\n");

    try {
      // 模拟用户记忆
      const completeMemory = [
        {
          memory_category: "profile",
          key_name: "current_company",
          value_content: "腾讯",
        },
        {
          memory_category: "profile",
          key_name: "tech_direction",
          value_content: "推荐算法",
        },
        { memory_category: "profile", key_name: "level", value_content: "P8" },
        {
          memory_category: "preference",
          key_name: "expected_salary",
          value_content: "35万",
        },
      ];

      this.database.getUserMemory = async () => completeMemory;

      console.log("🔸 测试完整信息触发条件");
      const triggerResult = await this.userManager.checkRecommendationTrigger(
        this.testUserId,
        this.testSessionId
      );
      console.log("✅ 触发结果:", {
        canTrigger: triggerResult.canTrigger,
        triggerType: triggerResult.triggerType,
        userInfo: triggerResult.userInfo,
      });
    } catch (error) {
      console.error("❌ 推荐触发条件测试失败:", error);
    }
  }

  async testCompleteRecommendationFlow() {
    console.log("\n📝 测试6: 完整推荐流程\n");

    try {
      // 模拟触发结果
      const mockTriggerResult = {
        canTrigger: true,
        triggerType: "complete",
        userInfo: {
          company: "腾讯",
          techDirection: "推荐算法",
          level: "P8",
          salary: "35万",
        },
      };

      console.log("🔸 测试完整推荐流程");
      const flowResult = await this.userManager.triggerRecommendationFlow(
        this.testUserId,
        this.testSessionId,
        mockTriggerResult
      );

      console.log("✅ 推荐流程结果:", {
        success: flowResult.success,
        triggerType: flowResult.triggerType,
        hasResponse: !!flowResult.response,
      });

      if (flowResult.response) {
        console.log(
          "📄 生成的回复:",
          flowResult.response.substring(0, 100) + "..."
        );
      }
    } catch (error) {
      console.error("❌ 完整推荐流程测试失败:", error);
    }
  }

  async cleanup() {
    console.log("\n🧹 清理测试数据...");
    try {
      // 清理测试数据
      console.log("✅ 测试数据清理完成");
    } catch (error) {
      console.error("❌ 清理失败:", error);
    }
  }
}

// 运行测试
async function runCompleteTest() {
  const test = new CompleteSystemTest();

  try {
    await test.initialize();
    await test.runAllTests();
  } catch (error) {
    console.error("测试执行失败:", error);
  } finally {
    await test.cleanup();
    process.exit(0);
  }
}

if (require.main === module) {
  runCompleteTest();
}

module.exports = CompleteSystemTest;
