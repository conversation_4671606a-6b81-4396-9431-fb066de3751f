/**
 * AI招聘助手 - Web测试服务器
 * 用于手动测试前端界面
 */

require("dotenv").config({ path: "../.env" });
const express = require("express");
const cors = require("cors");
const path = require("path");

// 导入核心服务
const MessageProcessor = require("./core/系统核心/message-processor");
const DatabaseManager = require("./core/数据管理/database-manager");
const UserManager = require("./core/业务服务/user-manager");

const app = express();
const PORT = 3000;

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.static(path.join(__dirname, "../public")));

// 全局变量
let messageProcessor;
let database;
let userManager;
let sessions = new Map();

// 初始化服务
async function initializeServices() {
  try {
    console.log("🚀 正在初始化AI招聘助手服务...");

    // 创建配置对象
    const config = {
      supabaseUrl: process.env.SUPABASE_URL,
      supabaseKey: process.env.SUPABASE_ANON_KEY,
      deepseekApiKey: process.env.DEEPSEEK_API_KEY,
      qwenApiKey: process.env.QWEN_API_KEY,
      deepseekEndpoint: process.env.DEEPSEEK_ENDPOINT,
      qwenEndpoint: process.env.QWEN_ENDPOINT,

      // 添加必要的方法
      getAIConfig() {
        return {
          deepseekApiKey: this.deepseekApiKey,
          qwenApiKey: this.qwenApiKey,
          deepseekEndpoint: this.deepseekEndpoint,
          qwenEndpoint: this.qwenEndpoint,
          maxTokens: 2000,
          temperature: 0.7,
          timeout: 30000,
          dailyCostLimit: 50,
        };
      },

      getDatabaseConfig() {
        return {
          supabaseUrl: this.supabaseUrl,
          supabaseKey: this.supabaseKey,
        };
      },
    };

    // 初始化数据库连接
    database = new DatabaseManager(config);
    await database.connect();

    // 初始化用户管理器
    userManager = new UserManager(database, config);

    // 初始化消息处理器
    messageProcessor = new MessageProcessor({
      database: database,
      userManager: userManager,
      config: config,
    });

    // 跳过完整初始化，避免TechMapper等组件的问题
    // await messageProcessor.initialize();

    // 手动初始化必要的组件
    const AIServices = require("./core/数据管理/ai-services");
    messageProcessor.aiServices = new AIServices(config.getAIConfig());
    await messageProcessor.aiServices.initialize();

    const ActiveRecommender = require("./core/业务服务/active-recommender");
    messageProcessor.activeRecommender = new ActiveRecommender(
      database,
      config
    );
    await messageProcessor.activeRecommender.initialize();

    console.log("✅ 服务初始化成功");
  } catch (error) {
    console.error("❌ 服务初始化失败:", error);
    throw error;
  }
}

// 获取开场白
app.get("/api/init", async (req, res) => {
  try {
    const sessionId = `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

    // 创建真实用户（如果不存在）
    let user;
    try {
      user = await database.getUserByEmail("<EMAIL>");
    } catch (error) {
      // 用户不存在，创建新用户
      user = await database.createUser({
        email: "<EMAIL>",
        name: "Web测试用户",
        source: "web",
      });
    }

    // 创建聊天会话
    const dbSession = await database.createChatSession({
      userId: user.id,
      sessionUuid: sessionId,
      entrySourceUrl: "web-test",
      initialIntent: "job_search",
    });

    // 存储会话
    sessions.set(sessionId, {
      id: sessionId,
      dbSessionId: dbSession.id,
      userId: user.id,
      messages: [],
      createdAt: new Date(),
    });

    // 返回正确的开场白
    const welcomeMessage = `您好！我是AI招聘助手Katrina🤖

我专门帮助技术人才找到心仪的工作机会。我可以：
• 🎯 根据您的技术背景推荐匹配职位
• 📊 分析不同公司的发展机会
• 💰 提供薪资和职级建议
• 🚀 匹配您的职业发展规划

请告诉我您的技术方向和求职需求，我来为您量身定制职位推荐！

同时，您也可以点击左下角的邮箱按钮，分享您的个人邮箱，我们可以更好的保持联系，定期的给您推送合适的职位。`;

    res.json({
      success: true,
      sessionId: sessionId,
      message: {
        type: "welcome",
        content: welcomeMessage,
        timestamp: new Date().toISOString(),
      },
    });
  } catch (error) {
    console.error("❌ 获取开场白失败:", error);
    res.status(500).json({
      success: false,
      error: "获取开场白失败: " + error.message,
    });
  }
});

// 处理聊天消息
app.post("/api/chat", async (req, res) => {
  try {
    const { sessionId, message } = req.body;

    if (!sessionId || !message) {
      return res.status(400).json({
        success: false,
        error: "缺少必要参数",
      });
    }

    const startTime = Date.now();

    // 获取会话
    let session = sessions.get(sessionId);
    if (!session) {
      return res.status(404).json({
        success: false,
        error: "会话不存在",
      });
    }

    console.log(`📝 处理消息: "${message}"`);

    // 保存用户消息到数据库
    await database.saveChatMessage({
      sessionId: session.dbSessionId,
      messageType: "user",
      content: message,
    });

    // 添加用户消息到本地历史
    session.messages.push({
      type: "user",
      content: message,
      timestamp: new Date().toISOString(),
    });

    // 获取用户信息状态
    const infoState = await userManager.getUserInfoState(session.userId);

    // 提取用户信息
    const extractedInfo = await userManager.extractUserInfo(message);
    let updatedInfoState = infoState;

    if (extractedInfo && Object.keys(extractedInfo).length > 0) {
      console.log("📝 解析到用户信息:", extractedInfo);
      updatedInfoState = await userManager.updateUserInfoState(
        session.userId,
        extractedInfo
      );
    }

    let aiReply;
    let metadata = {
      type: "general_response",
      processingTime: 0,
    };

    // 检查是否满足推荐条件
    if (updatedInfoState.可推荐) {
      console.log("✅ 满足推荐条件，触发职位推荐");

      const dbSession = await database.getChatSessionById(session.dbSessionId);
      const recommendationResult =
        await messageProcessor.activeRecommender.triggerJobRecommendation(
          session.userId,
          updatedInfoState,
          dbSession,
          userManager
        );

      aiReply = recommendationResult.content;
      metadata = {
        ...metadata,
        ...recommendationResult.metadata,
        type: recommendationResult.type,
      };
    } else {
      // 使用AI生成回复
      const prompt = `用户说: "${message}"\n\n请作为AI招聘助手Katrina回复，要求：\n1. 友好专业\n2. 引导用户提供更多信息\n3. 不超过100字`;

      aiReply = await messageProcessor.aiServices.generateResponse(prompt, {
        maxTokens: 200,
        temperature: 0.7,
      });
    }

    // 保存AI回复到数据库
    await database.saveChatMessage({
      sessionId: session.dbSessionId,
      messageType: "assistant",
      content: aiReply,
      metadata: metadata,
    });

    // 添加助手回复到本地历史
    session.messages.push({
      type: "assistant",
      content: aiReply,
      timestamp: new Date().toISOString(),
      metadata: metadata,
    });

    const processingTime = Date.now() - startTime;
    metadata.processingTime = processingTime;

    console.log(`🤖 AI回复: "${aiReply.substring(0, 50)}..."`);
    console.log(`⏱️ 处理时间: ${processingTime}ms`);

    res.json({
      success: true,
      message: {
        type: "response",
        content: aiReply,
        timestamp: new Date().toISOString(),
        metadata: metadata,
      },
    });
  } catch (error) {
    console.error("❌ 处理消息失败:", error);
    res.status(500).json({
      success: false,
      error: "处理消息失败: " + error.message,
    });
  }
});

// 获取会话统计
app.get("/api/stats/:sessionId", (req, res) => {
  const { sessionId } = req.params;
  const session = sessions.get(sessionId);

  if (!session) {
    return res.status(404).json({
      success: false,
      error: "会话不存在",
    });
  }

  const stats = {
    sessionId: sessionId,
    messageCount: session.messages.length,
    createdAt: session.createdAt,
    lastActivity:
      session.messages.length > 0
        ? session.messages[session.messages.length - 1].timestamp
        : session.createdAt,
  };

  res.json({
    success: true,
    stats: stats,
  });
});

// 健康检查
app.get("/api/health", (req, res) => {
  res.json({
    success: true,
    status: "healthy",
    timestamp: new Date().toISOString(),
    services: {
      messageProcessor: !!messageProcessor,
      database: !!database,
    },
  });
});

// 启动服务器
async function startServer() {
  try {
    await initializeServices();

    app.listen(PORT, () => {
      console.log(`\n🌐 AI招聘助手测试服务器启动成功！`);
      console.log(`📱 前端地址: http://localhost:${PORT}`);
      console.log(`🔗 API地址: http://localhost:${PORT}/api`);
      console.log(`\n📋 可用接口:`);
      console.log(`   GET  /api/init - 获取开场白`);
      console.log(`   POST /api/chat - 发送消息`);
      console.log(`   GET  /api/stats/:sessionId - 获取统计`);
      console.log(`   GET  /api/health - 健康检查`);
      console.log(`\n🎯 现在您可以在浏览器中测试完整的AI招聘助手了！`);
    });
  } catch (error) {
    console.error("❌ 服务器启动失败:", error);
    process.exit(1);
  }
}

startServer();
