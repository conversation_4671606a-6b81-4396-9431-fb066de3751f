/**
 * 真实一问一答对话测试
 * 模拟候选人挤牙膏式提供信息的真实场景
 */

require("dotenv").config({ path: "../.env" });

const DatabaseManager = require('./core/数据管理/database-manager');
const MessageProcessor = require('./core/系统核心/message-processor');
const UserManager = require('./core/业务服务/user-manager');
const ActiveRecommender = require('./core/业务服务/active-recommender');
const Utilities = require('./core/工具库/utilities');

class RealConversationTest {
  constructor() {
    this.realUserId = 623; // 真实用户ID
    this.testSessionId = 'conversation-' + Date.now();
    this.conversationHistory = [];
    this.currentStep = 0;
    
    // 模拟真实对话场景
    this.conversationScenario = [
      {
        step: 1,
        userMessage: "你好",
        expectedResponse: "问候并询问基本信息",
        description: "用户初次接触"
      },
      {
        step: 2,
        userMessage: "我想找工作",
        expectedResponse: "询问当前公司和技术方向",
        description: "表达求职意向"
      },
      {
        step: 3,
        userMessage: "我在做算法",
        expectedResponse: "询问具体算法方向和当前公司",
        description: "提供部分技术信息"
      },
      {
        step: 4,
        userMessage: "推荐算法，在腾讯",
        expectedResponse: "询问职级和薪资期望",
        description: "提供公司和技术方向"
      },
      {
        step: 5,
        userMessage: "我是10级",
        expectedResponse: "询问薪资期望",
        description: "提供职级信息"
      },
      {
        step: 6,
        userMessage: "期望年薪150万吧",
        expectedResponse: "触发推荐或询问城市偏好",
        description: "提供薪资期望，可能触发推荐"
      },
      {
        step: 7,
        userMessage: "有没有大厂的机会？",
        expectedResponse: "被动推荐触发，返回4×4推荐",
        description: "主动询问特定公司类型"
      }
    ];
  }

  async initialize() {
    try {
      console.log('🚀 初始化真实对话测试...');
      
      // 初始化数据库
      const dbConfig = {
        supabaseUrl: process.env.SUPABASE_URL,
        supabaseKey: process.env.SUPABASE_ANON_KEY,
      };
      
      this.database = new DatabaseManager(dbConfig);
      await this.database.connect();
      
      // 初始化各个模块
      this.messageProcessor = new MessageProcessor(
        this.database,
        null, // aiServices
        null, // chatInterface
        {}   // config
      );
      
      this.userManager = new UserManager(this.database, {});
      await this.userManager.initialize();
      
      this.activeRecommender = new ActiveRecommender(this.database, {});
      await this.activeRecommender.initialize();
      
      this.utilities = new Utilities();
      
      console.log('✅ 系统初始化完成\n');
    } catch (error) {
      console.error('❌ 初始化失败:', error);
      throw error;
    }
  }

  async runConversationTest() {
    try {
      console.log('🗣️ 开始真实对话测试\n');
      console.log(`👤 测试用户: ID ${this.realUserId}`);
      console.log(`📅 测试时间: ${new Date().toISOString()}\n`);

      for (const scenario of this.conversationScenario) {
        await this.processConversationStep(scenario);
        
        // 每步之间稍作停顿，模拟真实对话
        await this.sleep(1000);
      }

      console.log('\n🎉 对话测试完成！');
      this.generateConversationSummary();

    } catch (error) {
      console.error('❌ 对话测试失败:', error);
    }
  }

  async processConversationStep(scenario) {
    console.log(`\n📝 第${scenario.step}步: ${scenario.description}`);
    console.log(`👤 用户: "${scenario.userMessage}"`);
    
    const stepStartTime = Date.now();
    
    try {
      // 1. 检测推荐意图
      const passiveAnalysis = this.activeRecommender.analyzePassiveRecommendationRequest(scenario.userMessage);
      
      // 2. 提取信息
      const extractedInfo = this.extractInformationFromMessage(scenario.userMessage);
      
      // 3. 保存用户消息和提取的信息
      await this.saveUserMessage(scenario.userMessage, extractedInfo);
      
      // 4. 检查推荐触发条件
      const triggerResult = await this.userManager.checkRecommendationTrigger(
        this.realUserId, 
        this.testSessionId
      );
      
      // 5. 生成系统回复
      const systemResponse = await this.generateSystemResponse(
        scenario,
        passiveAnalysis,
        extractedInfo,
        triggerResult
      );
      
      const stepDuration = Date.now() - stepStartTime;
      
      // 记录对话历史
      this.conversationHistory.push({
        step: scenario.step,
        userMessage: scenario.userMessage,
        systemResponse: systemResponse,
        extractedInfo: extractedInfo,
        passiveAnalysis: passiveAnalysis,
        triggerResult: triggerResult,
        duration: stepDuration
      });
      
      console.log(`🤖 系统: ${systemResponse}`);
      console.log(`⏱️ 处理时间: ${stepDuration}ms`);
      
      // 如果触发了推荐，显示推荐详情
      if (triggerResult.canTrigger && passiveAnalysis.isPassiveRecommendation) {
        await this.showRecommendationDetails(triggerResult);
      }
      
    } catch (error) {
      console.error(`❌ 第${scenario.step}步处理失败:`, error.message);
    }
  }

  extractInformationFromMessage(message) {
    const info = {};
    
    // 提取薪资
    const salary = this.utilities.extractSalaryFromMessage(message);
    if (salary) info.salary = salary;
    
    // 提取城市
    const city = this.utilities.extractCity(message);
    if (city) info.city = city;
    
    // 检测公司信息
    const companies = ['腾讯', '阿里', '字节', '百度', '美团', '京东'];
    for (const company of companies) {
      if (message.includes(company)) {
        info.company = company;
        break;
      }
    }
    
    // 检测技术方向
    const techDirections = ['推荐算法', '搜索算法', '广告算法', '大模型', 'NLP', '计算机视觉'];
    for (const tech of techDirections) {
      if (message.includes(tech)) {
        info.techDirection = tech;
        break;
      }
    }
    
    // 检测职级
    const levelPatterns = [/(\d+)级/, /P(\d+)/, /T(\d+)/, /L(\d+)/];
    for (const pattern of levelPatterns) {
      const match = message.match(pattern);
      if (match) {
        info.level = match[0];
        break;
      }
    }
    
    return info;
  }

  async saveUserMessage(message, extractedInfo) {
    try {
      // 保存用户消息
      await this.database.saveUserMemory(
        this.realUserId,
        'conversation',
        `message_${Date.now()}`,
        message,
        0.9,
        this.testSessionId
      );
      
      // 保存提取的信息
      for (const [key, value] of Object.entries(extractedInfo)) {
        const category = ['salary', 'city'].includes(key) ? 'preference' : 'profile';
        const keyName = key === 'salary' ? 'expected_salary' : 
                       key === 'city' ? 'preferred_city' :
                       key === 'company' ? 'current_company' :
                       key === 'techDirection' ? 'tech_direction' : key;
        
        await this.database.saveUserMemory(
          this.realUserId,
          category,
          keyName,
          value,
          0.8,
          this.testSessionId
        );
      }
      
    } catch (error) {
      console.error('❌ 保存用户消息失败:', error);
    }
  }

  async generateSystemResponse(scenario, passiveAnalysis, extractedInfo, triggerResult) {
    try {
      // 如果检测到被动推荐请求且满足触发条件
      if (passiveAnalysis.isPassiveRecommendation && triggerResult.canTrigger) {
        return `根据您询问的${passiveAnalysis.primaryCompanyType?.name || '职位'}，我为您推荐以下机会：\n\n[推荐职位将在下方显示]`;
      }
      
      // 如果满足触发条件但不是被动推荐
      if (triggerResult.canTrigger) {
        return `根据您提供的信息，我为您推荐一些合适的职位：\n\n[推荐职位将在下方显示]`;
      }
      
      // 根据缺失信息生成询问
      const missingInfo = triggerResult.missingInfo || [];
      
      if (missingInfo.length > 0) {
        const questions = [];
        
        if (missingInfo.includes('所在公司')) {
          questions.push('您目前在哪家公司工作？');
        }
        if (missingInfo.includes('技术方向')) {
          questions.push('您主要做什么技术方向？');
        }
        if (missingInfo.includes('职级或期望薪酬')) {
          questions.push('您的职级是什么？或者期望薪酬范围是多少？');
        }
        
        return questions.length > 0 ? questions[0] : '请告诉我更多关于您的背景信息。';
      }
      
      // 默认回复
      return '好的，我了解了。还有其他信息可以分享吗？';
      
    } catch (error) {
      console.error('❌ 生成系统回复失败:', error);
      return '抱歉，系统处理中遇到问题，请稍后再试。';
    }
  }

  async showRecommendationDetails(triggerResult) {
    try {
      console.log('\n🎯 触发推荐详情:');
      console.log(`   触发类型: ${triggerResult.triggerType}`);
      console.log(`   用户信息完整度:`);
      console.log(`     公司: ${triggerResult.userInfo?.company || '缺失'}`);
      console.log(`     技术方向: ${triggerResult.userInfo?.techDirection || '缺失'}`);
      console.log(`     职级: ${triggerResult.userInfo?.level || '缺失'}`);
      console.log(`     薪资: ${triggerResult.userInfo?.salary || '缺失'}`);
      
      // 执行实际推荐
      const recommendationResult = await this.userManager.triggerRecommendationFlow(
        this.realUserId,
        this.testSessionId,
        triggerResult
      );
      
      if (recommendationResult.success && recommendationResult.recommendations.recommendations) {
        console.log('\n💼 推荐职位:');
        recommendationResult.recommendations.recommendations.forEach((rec, i) => {
          console.log(`   ${i+1}. 【${rec.companyType}】${rec.job?.job_title || '职位'}`);
          console.log(`      公司: ${rec.job?.companies?.company_name || '未知'}`);
          console.log(`      薪资: ${rec.job?.salary_min || 0}-${rec.job?.salary_max || 0}万`);
          console.log(`      匹配度: ${rec.matchScore}%`);
        });
      }
      
    } catch (error) {
      console.error('❌ 显示推荐详情失败:', error);
    }
  }

  generateConversationSummary() {
    console.log('\n📊 ==================== 对话测试总结 ====================');
    console.log(`💬 总对话轮数: ${this.conversationHistory.length}`);
    
    const totalDuration = this.conversationHistory.reduce((sum, step) => sum + step.duration, 0);
    console.log(`⏱️ 总处理时间: ${totalDuration}ms`);
    
    const avgDuration = totalDuration / this.conversationHistory.length;
    console.log(`📈 平均响应时间: ${avgDuration.toFixed(0)}ms`);
    
    // 信息收集进度
    const finalStep = this.conversationHistory[this.conversationHistory.length - 1];
    if (finalStep.triggerResult.canTrigger) {
      console.log(`✅ 信息收集: 完成 (${finalStep.triggerResult.triggerType})`);
    } else {
      console.log(`⚠️ 信息收集: 未完成，缺少 [${finalStep.triggerResult.missingInfo?.join(', ')}]`);
    }
    
    // 推荐触发情况
    const recommendationSteps = this.conversationHistory.filter(step => 
      step.triggerResult.canTrigger && step.passiveAnalysis.isPassiveRecommendation
    );
    console.log(`🎯 推荐触发次数: ${recommendationSteps.length}`);
    
    console.log('\n📝 详细对话记录:');
    this.conversationHistory.forEach((step, i) => {
      console.log(`${i+1}. 👤 "${step.userMessage}"`);
      console.log(`   🤖 "${step.systemResponse}"`);
      console.log(`   📊 提取信息: ${Object.keys(step.extractedInfo).length > 0 ? JSON.stringify(step.extractedInfo) : '无'}`);
      console.log(`   ⏱️ ${step.duration}ms\n`);
    });
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 运行测试
async function runConversationTest() {
  const test = new RealConversationTest();
  
  try {
    await test.initialize();
    await test.runConversationTest();
  } catch (error) {
    console.error('❌ 对话测试执行失败:', error);
  }
}

if (require.main === module) {
  runConversationTest();
}

module.exports = RealConversationTest;
