/**
 * 测试新的推荐逻辑
 * 验证4×4推荐规则和信息收集触发条件
 */

// 加载环境变量
require("dotenv").config({ path: "../.env" });

const DatabaseManager = require('./core/数据管理/database-manager');
const PassiveRecommender = require('./core/业务服务/passive-recommender');
const MessageProcessor = require('./core/系统核心/message-processor');

class RecommendationLogicTest {
  constructor() {
    this.testUserId = 1001; // 固定测试用户ID
  }

  async initialize() {
    try {
      console.log('🚀 初始化推荐逻辑测试...');
      
      // 初始化数据库
      const dbConfig = {
        supabaseUrl: process.env.SUPABASE_URL,
        supabaseKey: process.env.SUPABASE_ANON_KEY,
      };
      
      this.database = new DatabaseManager(dbConfig);
      await this.database.connect();
      
      // 初始化推荐引擎
      this.recommender = new PassiveRecommender(this.database, {});
      await this.recommender.initialize();
      
      console.log('✅ 测试环境初始化完成');
    } catch (error) {
      console.error('❌ 初始化失败:', error);
      throw error;
    }
  }

  async runAllTests() {
    try {
      console.log('\n🧪 开始推荐逻辑测试...\n');

      // 测试1: 4×4推荐规则
      await this.test4x4RecommendationRules();

      // 测试2: 信息收集触发条件
      await this.testTriggerConditions();

      // 测试3: 被动推荐
      await this.testPassiveRecommendation();

      console.log('\n🎉 所有测试完成！');
    } catch (error) {
      console.error('\n❌ 测试失败:', error);
      throw error;
    }
  }

  async test4x4RecommendationRules() {
    console.log('📝 测试1: 4×4推荐规则\n');

    try {
      // 准备测试用户档案
      const testProfile = {
        id: this.testUserId,
        primary_tech_direction_id: 725, // 推荐算法
        expected_compensation_min: 200,
        expected_compensation_max: 300,
        candidate_standard_level_min: 6,
        candidate_standard_level_max: 7
      };

      // 模拟数据库查询
      this.database.getCandidateProfile = async (userId) => {
        if (userId === this.testUserId) {
          return testProfile;
        }
        return null;
      };

      // 模拟职位搜索
      this.database.searchJobs = async (criteria) => {
        return [
          {
            id: 1,
            job_title: '推荐算法工程师',
            companies: { company_name: '字节跳动', company_type: '头部大厂' },
            salary_min: 200,
            salary_max: 350,
            primary_tech_direction_id: 725,
            job_standard_level_min: 6,
            job_standard_level_max: 7,
            job_description: '负责推荐系统的算法设计和优化',
            work_location: '北京'
          },
          {
            id: 2,
            job_title: '算法专家',
            companies: { company_name: '中兴通讯', company_type: '国企' },
            salary_min: 180,
            salary_max: 280,
            primary_tech_direction_id: 725,
            job_standard_level_min: 6,
            job_standard_level_max: 8,
            job_description: '从事机器学习算法研发',
            work_location: '深圳'
          },
          {
            id: 3,
            job_title: '高级算法工程师',
            companies: { company_name: '得物', company_type: '中型公司' },
            salary_min: 220,
            salary_max: 320,
            primary_tech_direction_id: 725,
            job_standard_level_min: 7,
            job_standard_level_max: 8,
            job_description: '电商推荐算法优化',
            work_location: '上海'
          },
          {
            id: 4,
            job_title: '算法工程师',
            companies: { company_name: '小虾米公司', company_type: '创业型公司' },
            salary_min: 150,
            salary_max: 250,
            primary_tech_direction_id: 725,
            job_standard_level_min: 5,
            job_standard_level_max: 7,
            job_description: '创新算法研发',
            work_location: '杭州'
          }
        ];
      };

      // 测试默认推荐（无排除）
      console.log('🔸 测试默认推荐规则');
      const defaultResult = await this.recommender.generate4x4Recommendations(this.testUserId);
      console.log('✅ 默认推荐结果:');
      console.log(`  推荐模式: [${defaultResult.pattern.join(', ')}]`);
      console.log(`  推荐数量: ${defaultResult.recommendations.length}`);
      defaultResult.recommendations.forEach((rec, i) => {
        console.log(`  ${i+1}. ${rec.companyType} - ${rec.job.job_title} (匹配度: ${rec.matchScore}%)`);
      });

      // 测试排除头部大厂
      console.log('\n🔸 测试排除头部大厂');
      const excludeAResult = await this.recommender.generate4x4Recommendations(this.testUserId, ['A']);
      console.log('✅ 排除A后推荐结果:');
      console.log(`  推荐模式: [${excludeAResult.pattern.join(', ')}]`);
      console.log(`  推荐数量: ${excludeAResult.recommendations.length}`);

      // 测试被动推荐（只推荐国企）
      console.log('\n🔸 测试被动推荐（只推荐国企）');
      const passiveResult = await this.recommender.generate4x4Recommendations(this.testUserId, [], 'B');
      console.log('✅ 被动推荐结果:');
      console.log(`  推荐模式: [${passiveResult.pattern.join(', ')}]`);
      console.log(`  目标类型: ${passiveResult.targetType}`);

    } catch (error) {
      console.error('❌ 4×4推荐规则测试失败:', error);
    }
  }

  async testTriggerConditions() {
    console.log('\n📝 测试2: 信息收集触发条件\n');

    try {
      // 创建消息处理器实例
      const messageProcessor = new MessageProcessor(this.database, null, null, {});

      // 测试场景1：完整信息
      console.log('🔸 测试场景1：完整信息（公司+技术方向+职级+薪酬）');
      const completeMemory = [
        { memory_category: 'profile', key_name: 'current_company', value_content: '阿里巴巴' },
        { memory_category: 'profile', key_name: 'tech_direction', value_content: '推荐算法' },
        { memory_category: 'profile', key_name: 'level', value_content: 'P7' },
        { memory_category: 'preference', key_name: 'expected_salary', value_content: '30-40万' }
      ];

      this.database.getUserMemory = async (userId) => {
        if (userId === this.testUserId) return completeMemory;
        return [];
      };

      const completeResult = await messageProcessor.checkRecommendationTrigger(this.testUserId);
      console.log('✅ 完整信息测试结果:');
      console.log(`  可触发推荐: ${completeResult.canTrigger}`);
      console.log(`  触发类型: ${completeResult.triggerType}`);

      // 测试场景2：缺少薪酬信息
      console.log('\n🔸 测试场景2：缺少薪酬信息（公司+技术方向+职级）');
      const noSalaryMemory = [
        { memory_category: 'profile', key_name: 'current_company', value_content: '腾讯' },
        { memory_category: 'profile', key_name: 'tech_direction', value_content: '搜索算法' },
        { memory_category: 'profile', key_name: 'level', value_content: '10级' }
      ];

      this.database.getUserMemory = async (userId) => {
        if (userId === this.testUserId) return noSalaryMemory;
        return [];
      };

      const noSalaryResult = await messageProcessor.checkRecommendationTrigger(this.testUserId);
      console.log('✅ 缺少薪酬测试结果:');
      console.log(`  可触发推荐: ${noSalaryResult.canTrigger}`);
      console.log(`  触发类型: ${noSalaryResult.triggerType}`);

      // 测试场景3：信息不足
      console.log('\n🔸 测试场景3：信息不足（只有公司）');
      const insufficientMemory = [
        { memory_category: 'profile', key_name: 'current_company', value_content: '字节跳动' }
      ];

      this.database.getUserMemory = async (userId) => {
        if (userId === this.testUserId) return insufficientMemory;
        return [];
      };

      const insufficientResult = await messageProcessor.checkRecommendationTrigger(this.testUserId);
      console.log('✅ 信息不足测试结果:');
      console.log(`  可触发推荐: ${insufficientResult.canTrigger}`);
      console.log(`  缺少信息: [${insufficientResult.missingInfo?.join(', ')}]`);

    } catch (error) {
      console.error('❌ 触发条件测试失败:', error);
    }
  }

  async testPassiveRecommendation() {
    console.log('\n📝 测试3: 被动推荐逻辑\n');

    try {
      console.log('🔸 模拟用户询问："有没有外企的机会？"');
      
      // 这里应该检测到用户询问特定公司类型
      // 然后调用被动推荐逻辑
      
      console.log('✅ 被动推荐逻辑：');
      console.log('  1. 检测到用户询问"外企"');
      console.log('  2. 基于已收集信息筛选外企职位');
      console.log('  3. 如果有匹配 → 4×4推荐全部换成外企');
      console.log('  4. 如果没有 → 告知没有外企职位');

    } catch (error) {
      console.error('❌ 被动推荐测试失败:', error);
    }
  }

  async cleanup() {
    console.log('\n🧹 清理测试数据...');
    try {
      // 这里可以清理测试数据
      console.log('✅ 测试数据清理完成');
    } catch (error) {
      console.error('❌ 清理失败:', error);
    }
  }
}

// 运行测试
async function runTest() {
  const test = new RecommendationLogicTest();
  
  try {
    await test.initialize();
    await test.runAllTests();
  } catch (error) {
    console.error('测试执行失败:', error);
  } finally {
    await test.cleanup();
    process.exit(0);
  }
}

if (require.main === module) {
  runTest();
}

module.exports = RecommendationLogicTest;
