/**
 * 修复后的系统测试 - 真实API调用
 * 测试开场白 + <PERSON>wen分析 + DeepSeek回复 + 数据库职位推荐
 */

require("dotenv").config({ path: "../.env" });

const DatabaseManager = require("./core/数据管理/database-manager");
const MessageProcessor = require("./core/系统核心/message-processor");
const UserManager = require("./core/业务服务/user-manager");
const AppConfig = require("./core/系统核心/app-config");
const { v4: uuidv4 } = require("uuid");

class FixedSystemTest {
  constructor() {
    this.realUserId = 623;
    this.testSessionId = uuidv4(); // 使用正确的UUID格式
    this.conversationHistory = [];

    // 完整对话流程：开场白 + 挤牙膏式信息收集
    this.conversationSteps = [
      {
        step: 0,
        userMessage: "__INIT__",
        description: "🎬 系统初始化 - 触发Katrina开场白",
        expectWelcome: true,
      },
      {
        step: 1,
        userMessage: "你好",
        description: "👤 用户回复开场白",
        expectHardcoded: true,
      },
      {
        step: 2,
        userMessage: "我想找工作",
        description: "👤 表达求职意向",
        expectHardcoded: true,
      },
      {
        step: 3,
        userMessage: "算法方面的",
        description: "👤 提供模糊技术信息",
        expectAI: true,
      },
      {
        step: 4,
        userMessage: "推荐算法，我在腾讯",
        description: "👤 提供具体信息",
        expectAI: true,
      },
      {
        step: 5,
        userMessage: "10级",
        description: "👤 提供职级信息",
        expectAI: true,
      },
      {
        step: 6,
        userMessage: "期望150万年薪",
        description: "👤 提供薪资期望",
        expectRecommendation: true,
      },
      {
        step: 7,
        userMessage: "有没有大厂的机会？",
        description: "👤 被动推荐请求",
        expectRecommendation: true,
      },
    ];
  }

  async initialize() {
    try {
      console.log("🚀 初始化修复后的系统测试...");

      // 直接使用数据库配置
      const dbConfig = {
        supabaseUrl: process.env.SUPABASE_URL,
        supabaseKey: process.env.SUPABASE_ANON_KEY,
      };

      // 初始化数据库
      this.database = new DatabaseManager(dbConfig);
      await this.database.connect();

      // 创建简化配置
      this.config = {
        getAIConfig: () => ({
          deepseekApiKey: process.env.DEEPSEEK_API_KEY,
          deepseekEndpoint: process.env.DEEPSEEK_ENDPOINT,
          qwenApiKey: process.env.QWEN_API_KEY,
          qwenEndpoint: process.env.QWEN_ENDPOINT,
          maxTokens: 1000,
          temperature: 0.7,
          timeout: 30000,
        }),
        getBusinessConfig: () => ({
          companyName: "HLG",
          teamName: "Felton团队",
          assistantName: "Katrina",
          assistantRole: "AI领域的猎头",
          maxSessionDuration: 24 * 60 * 60 * 1000, // 24小时
          defaultResponseTimeout: 30000,
        }),
      };

      // 初始化用户管理器
      this.userManager = new UserManager(this.database, this.config);
      await this.userManager.initialize();

      // 初始化消息处理器
      this.messageProcessor = new MessageProcessor({
        database: this.database,
        userManager: this.userManager,
        config: this.config,
      });
      await this.messageProcessor.initialize();

      console.log("✅ 系统初始化完成\n");
    } catch (error) {
      console.error("❌ 初始化失败:", error);
      throw error;
    }
  }

  async runFixedSystemTest() {
    try {
      console.log("🤖 开始修复后的系统测试\n");
      console.log(`📅 测试时间: ${new Date().toISOString()}`);
      console.log(`👤 测试用户: ID ${this.realUserId}`);
      console.log(
        `🔑 DeepSeek API: ${process.env.DEEPSEEK_API_KEY ? "已配置" : "未配置"}`
      );
      console.log(
        `🔑 Qwen API: ${process.env.QWEN_API_KEY ? "已配置" : "未配置"}\n`
      );

      for (const step of this.conversationSteps) {
        await this.processStep(step);
        await this.sleep(2000); // 模拟真实对话间隔
      }

      this.generateTestReport();
    } catch (error) {
      console.error("❌ 系统测试失败:", error);
    }
  }

  async processStep(step) {
    console.log(`\n📝 第${step.step}步: ${step.description}`);

    if (step.step === 0) {
      console.log(`🤖 系统消息: "${step.userMessage}"`);
    } else {
      console.log(`👤 用户: "${step.userMessage}"`);
    }

    const stepStartTime = Date.now();

    try {
      // 调用真实的消息处理器
      const messageData = {
        message: step.userMessage,
        userId: this.realUserId,
        sessionId: this.testSessionId,
        userEmail: "<EMAIL>", // 提供必需的email字段
      };

      const response = await this.messageProcessor.processMessage(messageData);

      const stepDuration = Date.now() - stepStartTime;

      // 记录对话历史
      this.conversationHistory.push({
        step: step.step,
        userMessage: step.userMessage,
        response: response,
        duration: stepDuration,
        timestamp: new Date().toISOString(),
        expectation: this.getExpectationType(step),
      });

      // 显示结果
      if (response.success) {
        const content =
          response.response?.content || response.response || "无回复";
        console.log(
          `🤖 Katrina: ${content.substring(0, 200)}${content.length > 200 ? "..." : ""}`
        );
        console.log(`⏱️ 处理时间: ${stepDuration}ms`);
        console.log(`🧠 意图识别: ${response.intent || "未识别"}`);

        // 验证期望
        this.validateExpectation(step, response);
      } else {
        console.log(`❌ 处理失败: ${response.error}`);
        console.log(`⏱️ 失败时间: ${stepDuration}ms`);
      }
    } catch (error) {
      const stepDuration = Date.now() - stepStartTime;
      console.error(`❌ 第${step.step}步处理异常:`, error.message);

      this.conversationHistory.push({
        step: step.step,
        userMessage: step.userMessage,
        error: error.message,
        duration: stepDuration,
        timestamp: new Date().toISOString(),
      });
    }
  }

  getExpectationType(step) {
    if (step.expectWelcome) return "welcome";
    if (step.expectHardcoded) return "hardcoded";
    if (step.expectAI) return "ai_generated";
    if (step.expectRecommendation) return "recommendation";
    return "unknown";
  }

  validateExpectation(step, response) {
    const content = response.response?.content || response.response || "";

    if (step.expectWelcome) {
      if (content.includes("我是AI领域的猎头Katrina")) {
        console.log("✅ 开场白验证通过");
      } else {
        console.log("❌ 开场白验证失败");
      }
    }

    if (step.expectHardcoded) {
      if (response.response?.metadata?.responseSource?.includes("hardcoded")) {
        console.log("✅ 硬编码回复验证通过");
      } else {
        console.log("⚠️ 可能不是硬编码回复");
      }
    }

    if (step.expectAI) {
      if (response.response?.metadata?.responseSource?.includes("ai")) {
        console.log("✅ AI生成回复验证通过");
      } else {
        console.log("⚠️ 可能不是AI生成回复");
      }
    }

    if (step.expectRecommendation) {
      if (content.includes("推荐") || content.includes("职位")) {
        console.log("✅ 推荐回复验证通过");
      } else {
        console.log("⚠️ 可能不包含推荐内容");
      }
    }
  }

  generateTestReport() {
    console.log(
      "\n📊 ==================== 修复后系统测试报告 ===================="
    );

    const totalSteps = this.conversationHistory.length;
    const successfulSteps = this.conversationHistory.filter(
      (h) => h.response?.success !== false && !h.error
    ).length;
    const failedSteps = totalSteps - successfulSteps;

    console.log(`🧪 总测试步骤: ${totalSteps}`);
    console.log(`✅ 成功处理: ${successfulSteps}`);
    console.log(`❌ 处理失败: ${failedSteps}`);
    console.log(
      `📈 成功率: ${((successfulSteps / totalSteps) * 100).toFixed(1)}%`
    );

    // 计算平均响应时间
    const successfulDurations = this.conversationHistory
      .filter((h) => !h.error)
      .map((h) => h.duration);

    if (successfulDurations.length > 0) {
      const avgDuration =
        successfulDurations.reduce((a, b) => a + b, 0) /
        successfulDurations.length;
      console.log(`⏱️ 平均响应时间: ${avgDuration.toFixed(0)}ms`);
    }

    // 功能验证统计
    const welcomeSteps = this.conversationHistory.filter(
      (h) => h.expectation === "welcome"
    ).length;
    const hardcodedSteps = this.conversationHistory.filter(
      (h) => h.expectation === "hardcoded"
    ).length;
    const aiSteps = this.conversationHistory.filter(
      (h) => h.expectation === "ai_generated"
    ).length;
    const recommendationSteps = this.conversationHistory.filter(
      (h) => h.expectation === "recommendation"
    ).length;

    console.log("\n🔍 功能验证统计:");
    console.log(`   开场白测试: ${welcomeSteps}次`);
    console.log(`   硬编码回复: ${hardcodedSteps}次`);
    console.log(`   AI生成回复: ${aiSteps}次`);
    console.log(`   推荐功能: ${recommendationSteps}次`);

    // 详细对话记录
    console.log("\n📝 详细对话记录:");
    this.conversationHistory.forEach((turn, i) => {
      console.log(
        `\n${i + 1}. ${turn.step === 0 ? "🤖" : "👤"} "${turn.userMessage}"`
      );

      if (turn.error) {
        console.log(`   ❌ 处理失败: ${turn.error}`);
      } else if (turn.response) {
        const content =
          turn.response.response?.content || turn.response.response || "无回复";
        console.log(
          `   🤖 "${content.substring(0, 100)}${content.length > 100 ? "..." : ""}"`
        );
        console.log(`   📊 意图: ${turn.response.intent || "未识别"}`);
        console.log(`   🎯 期望: ${turn.expectation}`);
      }

      console.log(`   ⏱️ ${turn.duration}ms`);
    });

    // 测试结论
    console.log("\n🎯 测试结论:");
    if (failedSteps === 0) {
      console.log("✅ 所有功能正常，系统修复成功");
    } else if (successfulSteps > failedSteps) {
      console.log("⚠️ 部分功能异常，需要进一步检查");
    } else {
      console.log("❌ 系统存在严重问题，需要重新修复");
    }

    // API调用验证
    console.log("\n🤖 AI模型分工验证:");
    console.log("   Qwen-Turbo: 用于意图分析和信息提取");
    console.log("   DeepSeek V3: 用于对话回复生成");
    console.log("   职位信息: 全部来自数据库，无AI虚构");
  }

  sleep(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}

// 运行测试
async function runTest() {
  const test = new FixedSystemTest();

  try {
    await test.initialize();
    await test.runFixedSystemTest();
  } catch (error) {
    console.error("❌ 测试执行失败:", error);
  }
}

if (require.main === module) {
  runTest();
}

module.exports = FixedSystemTest;
