/**
 * 真实环境推荐流程测试
 * 使用真实用户ID: 623 (<EMAIL>)
 */

require("dotenv").config({ path: "../.env" });

const DatabaseManager = require("./core/数据管理/database-manager");
const PassiveRecommender = require("./core/业务服务/passive-recommender");
const ActiveRecommender = require("./core/业务服务/active-recommender");
const UserManager = require("./core/业务服务/user-manager");
const MappingTables = require("./core/工具库/mapping-tables");
const Utilities = require("./core/工具库/utilities");

class RealRecommendationFlowTest {
  constructor() {
    this.realUserId = 623; // 真实用户ID
    this.testSessionId = "real-session-" + Date.now();
    this.testResults = {
      startTime: new Date(),
      steps: [],
      errors: [],
      finalResult: null,
    };
  }

  async initialize() {
    try {
      this.logStep("🚀 初始化真实环境测试", "开始");

      // 初始化数据库
      const dbConfig = {
        supabaseUrl: process.env.SUPABASE_URL,
        supabaseKey: process.env.SUPABASE_ANON_KEY,
      };

      this.database = new DatabaseManager(dbConfig);
      await this.database.connect();
      this.logStep("🗄️ 数据库连接", "成功");

      // 初始化各个模块
      this.passiveRecommender = new PassiveRecommender(this.database, {});
      await this.passiveRecommender.initialize();
      this.logStep("🎯 被动推荐引擎", "初始化成功");

      this.activeRecommender = new ActiveRecommender(this.database, {});
      await this.activeRecommender.initialize();
      this.logStep("🚀 主动推荐引擎", "初始化成功");

      this.userManager = new UserManager(this.database, {});
      await this.userManager.initialize();
      this.logStep("👤 用户管理器", "初始化成功");

      this.mappingTables = new MappingTables();
      this.mappingTables.database = this.database;
      this.logStep("📋 映射表管理器", "初始化成功");

      this.utilities = new Utilities();
      this.logStep("🔧 工具库", "初始化成功");

      console.log("✅ 系统初始化完成\n");
    } catch (error) {
      this.logError("初始化失败", error);
      throw error;
    }
  }

  async runCompleteFlow() {
    try {
      console.log("🧪 开始真实环境推荐流程测试\n");
      console.log(
        `👤 测试用户: ID ${this.realUserId} (<EMAIL>)`
      );
      console.log(`📅 测试时间: ${this.testResults.startTime.toISOString()}\n`);

      // 第一步：验证用户存在
      await this.step1_VerifyUser();

      // 第二步：模拟用户信息录入
      await this.step2_SimulateUserInput();

      // 第三步：信息提取与分类
      await this.step3_ExtractInformation();

      // 第四步：检测推荐意图
      await this.step4_DetectRecommendationIntent();

      // 第五步：检查推荐触发条件
      await this.step5_CheckTriggerConditions();

      // 第六步：执行4×4推荐逻辑
      await this.step6_Execute4x4Recommendation();

      // 第七步：职级映射查询
      await this.step7_LevelMapping();

      // 第八步：生成最终推荐
      await this.step8_GenerateFinalRecommendation();

      this.generateFinalReport();
    } catch (error) {
      this.logError("流程执行失败", error);
      this.generateErrorReport();
    }
  }

  async step1_VerifyUser() {
    console.log("📝 第一步：验证用户存在");

    try {
      const user = await this.database.getUserById(this.realUserId);
      if (!user) {
        throw new Error(`用户ID ${this.realUserId} 不存在`);
      }

      this.logStep("用户验证", `成功 - ${user.email}`);
      console.log(`✅ 用户信息: ${user.email}, 注册时间: ${user.created_at}\n`);

      return user;
    } catch (error) {
      this.logError("用户验证失败", error);
      throw error;
    }
  }

  async step2_SimulateUserInput() {
    console.log("📝 第二步：模拟用户信息录入");

    try {
      // 模拟用户对话消息
      const userMessages = [
        "我在腾讯做推荐算法，P7级别",
        "期望年薪130W，base北京",
        "有没有大厂的机会？",
      ];

      this.userInputData = {
        messages: userMessages,
        extractedInfo: {},
      };

      // 保存用户消息到记忆
      for (let i = 0; i < userMessages.length; i++) {
        const message = userMessages[i];
        console.log(`💬 用户消息 ${i + 1}: "${message}"`);

        // 模拟保存到数据库
        await this.database.saveUserMemory(
          this.realUserId,
          "conversation",
          `message_${Date.now()}_${i}`,
          message,
          0.9,
          this.testSessionId
        );
      }

      this.logStep("用户信息录入", `成功 - ${userMessages.length}条消息`);
      console.log("✅ 用户信息录入完成\n");
    } catch (error) {
      this.logError("用户信息录入失败", error);
      throw error;
    }
  }

  async step3_ExtractInformation() {
    console.log("📝 第三步：信息提取与分类");

    try {
      const allMessages = this.userInputData.messages.join(" ");

      // 提取薪资信息
      const salary = this.utilities.extractSalaryFromMessage(allMessages);
      console.log(`💰 提取薪资: ${salary || "未找到"}`);

      // 提取城市信息
      const city = this.utilities.extractCity(allMessages);
      console.log(`🏙️ 提取城市: ${city || "未找到"}`);

      // 提取职级信息
      const levels = this.mappingTables.extractLevelFromText(allMessages);
      console.log(
        `📊 提取职级: ${levels.length > 0 ? levels.join(", ") : "未找到"}`
      );

      // 保存提取的信息到用户记忆
      if (salary) {
        await this.database.saveUserMemory(
          this.realUserId,
          "preference",
          "expected_salary",
          salary,
          0.8,
          this.testSessionId
        );
      }

      if (city) {
        await this.database.saveUserMemory(
          this.realUserId,
          "preference",
          "preferred_city",
          city,
          0.8,
          this.testSessionId
        );
      }

      if (levels.length > 0) {
        await this.database.saveUserMemory(
          this.realUserId,
          "profile",
          "level",
          levels[0],
          0.8,
          this.testSessionId
        );
      }

      // 手动添加公司和技术方向信息
      await this.database.saveUserMemory(
        this.realUserId,
        "profile",
        "current_company",
        "腾讯",
        0.9,
        this.testSessionId
      );

      await this.database.saveUserMemory(
        this.realUserId,
        "profile",
        "tech_direction",
        "推荐算法",
        0.9,
        this.testSessionId
      );

      this.userInputData.extractedInfo = { salary, city, levels };
      this.logStep("信息提取", "成功");
      console.log("✅ 信息提取完成\n");
    } catch (error) {
      this.logError("信息提取失败", error);
      throw error;
    }
  }

  async step4_DetectRecommendationIntent() {
    console.log("📝 第四步：检测推荐意图");

    try {
      const lastMessage =
        this.userInputData.messages[this.userInputData.messages.length - 1];

      // 检测被动推荐请求
      const passiveAnalysis =
        this.activeRecommender.analyzePassiveRecommendationRequest(lastMessage);

      console.log(`🎯 被动推荐分析:`);
      console.log(
        `   是否被动推荐: ${passiveAnalysis.isPassiveRecommendation}`
      );
      console.log(
        `   置信度: ${(passiveAnalysis.confidence * 100).toFixed(1)}%`
      );
      console.log(
        `   主要公司类型: ${passiveAnalysis.primaryCompanyType?.name || "无"}`
      );
      console.log(
        `   推荐策略: ${passiveAnalysis.recommendation?.action || "无"}`
      );

      this.recommendationIntent = passiveAnalysis;
      this.logStep(
        "推荐意图检测",
        `成功 - ${passiveAnalysis.isPassiveRecommendation ? "被动推荐" : "无推荐意图"}`
      );
      console.log("✅ 推荐意图检测完成\n");
    } catch (error) {
      this.logError("推荐意图检测失败", error);
      throw error;
    }
  }

  async step5_CheckTriggerConditions() {
    console.log("📝 第五步：检查推荐触发条件");

    try {
      const triggerResult = await this.userManager.checkRecommendationTrigger(
        this.realUserId,
        this.testSessionId
      );

      console.log(`🔍 触发条件检查:`);
      console.log(`   可以触发: ${triggerResult.canTrigger}`);
      console.log(`   触发类型: ${triggerResult.triggerType || "无"}`);
      console.log(`   用户信息完整度:`);
      console.log(`     公司: ${triggerResult.userInfo?.company || "缺失"}`);
      console.log(
        `     技术方向: ${triggerResult.userInfo?.techDirection || "缺失"}`
      );
      console.log(`     职级: ${triggerResult.userInfo?.level || "缺失"}`);
      console.log(`     薪资: ${triggerResult.userInfo?.salary || "缺失"}`);

      this.triggerConditions = triggerResult;
      this.logStep(
        "触发条件检查",
        `成功 - ${triggerResult.canTrigger ? "满足条件" : "不满足条件"}`
      );
      console.log("✅ 触发条件检查完成\n");
    } catch (error) {
      this.logError("触发条件检查失败", error);
      throw error;
    }
  }

  async step6_Execute4x4Recommendation() {
    console.log("📝 第六步：执行4×4推荐逻辑");

    try {
      if (!this.triggerConditions.canTrigger) {
        console.log("⚠️ 不满足推荐触发条件，跳过推荐");
        this.logStep("4×4推荐执行", "跳过 - 不满足触发条件");
        return;
      }

      // 确定推荐参数
      let excludedTypes = [];
      let targetType = null;

      // 如果是被动推荐且检测到特定公司类型
      if (
        this.recommendationIntent.isPassiveRecommendation &&
        this.recommendationIntent.primaryCompanyType
      ) {
        const typeMapping = {
          头部大厂: "A",
          国企: "B",
          中型公司: "C",
          创业型公司: "D",
        };
        targetType =
          typeMapping[this.recommendationIntent.primaryCompanyType.name];
        console.log(
          `🎯 被动推荐目标类型: ${targetType} (${this.recommendationIntent.primaryCompanyType.name})`
        );
      }

      // 执行推荐
      const recommendationResult =
        await this.passiveRecommender.generate4x4Recommendations(
          this.realUserId,
          excludedTypes,
          targetType
        );

      console.log(`🎯 4×4推荐结果:`);
      console.log(`   推荐模式: [${recommendationResult.pattern.join(", ")}]`);
      console.log(
        `   推荐数量: ${recommendationResult.recommendations.length}`
      );
      console.log(`   目标类型: ${recommendationResult.targetType || "无"}`);
      console.log(
        `   排除类型: [${recommendationResult.excludedTypes.join(", ") || "无"}]`
      );

      if (recommendationResult.recommendations.length > 0) {
        console.log(`   推荐职位:`);
        recommendationResult.recommendations.forEach((rec, i) => {
          console.log(
            `     ${i + 1}. ${rec.companyType} - ${rec.job?.job_title || "职位"} (匹配度: ${rec.matchScore}%)`
          );
        });
      }

      this.recommendationResult = recommendationResult;
      this.logStep(
        "4×4推荐执行",
        `成功 - ${recommendationResult.recommendations.length}个推荐`
      );
      console.log("✅ 4×4推荐执行完成\n");
    } catch (error) {
      this.logError("4×4推荐执行失败", error);
      throw error;
    }
  }

  async step7_LevelMapping() {
    console.log("📝 第七步：职级映射查询");

    try {
      const userLevel = this.triggerConditions.userInfo?.level;
      const userCompany = this.triggerConditions.userInfo?.company;

      if (!userLevel) {
        console.log("⚠️ 未找到用户职级信息，跳过职级映射");
        this.logStep("职级映射查询", "跳过 - 无职级信息");
        return;
      }

      // 查询职级映射
      const levelMapping = await this.mappingTables.queryLevelMapping(
        userLevel,
        userCompany
      );

      if (levelMapping) {
        console.log(`📊 职级映射结果:`);
        console.log(`   原始职级: ${levelMapping.originalLevel}`);
        console.log(
          `   标准级别: P${levelMapping.standardLevelMin}-P${levelMapping.standardLevelMax}`
        );
        console.log(`   参考公司: ${levelMapping.companyName}`);
        console.log(
          `   置信度: ${(levelMapping.confidence * 100).toFixed(1)}%`
        );

        // 获取P级描述
        const pDescription = this.mappingTables.getPLevelDescription(
          levelMapping.standardLevelMin
        );
        console.log(`   职级描述: ${pDescription}`);
      } else {
        console.log("⚠️ 未找到匹配的职级映射");
      }

      this.levelMapping = levelMapping;
      this.logStep("职级映射查询", levelMapping ? "成功" : "未找到映射");
      console.log("✅ 职级映射查询完成\n");
    } catch (error) {
      this.logError("职级映射查询失败", error);
      throw error;
    }
  }

  async step8_GenerateFinalRecommendation() {
    console.log("📝 第八步：生成最终推荐");

    try {
      if (!this.triggerConditions.canTrigger) {
        console.log("⚠️ 不满足推荐条件，生成信息收集回复");

        const missingInfo = this.triggerConditions.missingInfo || [];
        const response = `我需要了解更多信息来为您推荐合适的职位。请告诉我：${missingInfo.join("、")}`;

        this.finalRecommendation = {
          type: "info_collection",
          response: response,
          missingInfo: missingInfo,
        };

        this.logStep("最终推荐生成", "信息收集回复");
        console.log(`📄 生成回复: ${response}\n`);
        return;
      }

      // 生成推荐回复
      let response = "";

      if (this.recommendationIntent.isPassiveRecommendation) {
        response = `根据您询问的${this.recommendationIntent.primaryCompanyType?.name || "职位"}，我为您推荐以下机会：\n\n`;
      } else {
        response = `基于您的背景信息（${this.triggerConditions.userInfo.company} ${this.triggerConditions.userInfo.level}，${this.triggerConditions.userInfo.techDirection}），我为您推荐：\n\n`;
      }

      if (
        this.recommendationResult &&
        this.recommendationResult.recommendations.length > 0
      ) {
        this.recommendationResult.recommendations.forEach((rec, i) => {
          response += `${i + 1}. 【${rec.companyType}】${rec.job?.job_title || "职位"}\n`;
          response += `   公司：${rec.job?.companies?.company_name || "未知"}\n`;
          response += `   薪资：${rec.job?.salary_min || 0}-${rec.job?.salary_max || 0}万\n`;
          response += `   匹配度：${rec.matchScore}%\n\n`;
        });

        response += "您对哪个职位比较感兴趣？我可以为您提供更详细的信息。";
      } else {
        response += "暂时没有找到完全匹配的职位，我会继续为您寻找合适的机会。";
      }

      this.finalRecommendation = {
        type: "job_recommendation",
        response: response,
        recommendations: this.recommendationResult?.recommendations || [],
        triggerType: this.triggerConditions.triggerType,
        levelMapping: this.levelMapping,
      };

      this.logStep("最终推荐生成", "成功");
      console.log(`📄 最终推荐回复:\n${response}\n`);
    } catch (error) {
      this.logError("最终推荐生成失败", error);
      throw error;
    }
  }

  generateFinalReport() {
    const endTime = new Date();
    const totalDuration =
      endTime.getTime() - this.testResults.startTime.getTime();

    console.log("🎉 ==================== 测试完成 ====================");
    console.log(`⏱️ 总执行时间: ${totalDuration}ms`);
    console.log(`✅ 成功步骤: ${this.testResults.steps.length}`);
    console.log(`❌ 失败步骤: ${this.testResults.errors.length}`);

    if (this.testResults.errors.length === 0) {
      console.log("\n🎯 推荐流程测试 - 全部成功！");

      if (this.finalRecommendation) {
        console.log(`\n📋 推荐类型: ${this.finalRecommendation.type}`);
        console.log(
          `🎯 触发类型: ${this.finalRecommendation.triggerType || "无"}`
        );

        if (
          this.finalRecommendation.recommendations &&
          this.finalRecommendation.recommendations.length > 0
        ) {
          console.log(
            `💼 推荐职位数: ${this.finalRecommendation.recommendations.length}`
          );
          console.log(
            `🏢 推荐模式: [${this.recommendationResult.pattern.join(", ")}]`
          );
        }

        if (this.levelMapping) {
          console.log(
            `📊 职级映射: ${this.levelMapping.originalLevel} → P${this.levelMapping.standardLevelMin}-P${this.levelMapping.standardLevelMax}`
          );
        }
      }
    } else {
      console.log("\n❌ 测试过程中发现问题:");
      this.testResults.errors.forEach((error, i) => {
        console.log(`${i + 1}. ${error.step}: ${error.error}`);
      });
    }

    console.log("\n📊 详细步骤执行情况:");
    this.testResults.steps.forEach((step, i) => {
      console.log(
        `${i + 1}. ${step.step}: ${step.result} (${step.duration}ms)`
      );
    });
  }

  generateErrorReport() {
    console.log("\n❌ ==================== 测试失败 ====================");
    console.log("🔍 排查建议:");

    this.testResults.errors.forEach((error, i) => {
      console.log(`\n${i + 1}. 失败位置: ${error.step}`);
      console.log(`   错误原因: ${error.error}`);

      // 提供具体的排查建议
      if (error.step.includes("数据库")) {
        console.log(`   排查建议: 检查数据库连接、权限配置、表结构`);
      } else if (error.step.includes("用户")) {
        console.log(`   排查建议: 检查用户ID是否存在、用户数据完整性`);
      } else if (error.step.includes("推荐")) {
        console.log(`   排查建议: 检查推荐引擎初始化、职位数据、匹配逻辑`);
      } else {
        console.log(`   排查建议: 检查代码逻辑、参数传递、异常处理`);
      }
    });
  }

  logStep(step, result) {
    this.testResults.steps.push({
      step: step,
      result: result,
      timestamp: new Date(),
      duration: Date.now() - this.testResults.startTime.getTime(),
    });
  }

  logError(step, error) {
    this.testResults.errors.push({
      step: step,
      error: error.message,
      timestamp: new Date(),
    });
    console.error(`❌ ${step}:`, error.message);
  }
}

// 运行测试
async function runRealTest() {
  const test = new RealRecommendationFlowTest();

  try {
    await test.initialize();
    await test.runCompleteFlow();
  } catch (error) {
    console.error("❌ 测试执行失败:", error);
  }
}

if (require.main === module) {
  runRealTest();
}

module.exports = RealRecommendationFlowTest;
