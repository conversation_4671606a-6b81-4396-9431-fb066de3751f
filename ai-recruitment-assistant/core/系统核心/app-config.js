/**
 * AI招聘助手系统 - 应用配置管理器
 *
 * 核心职责：
 * - 系统配置管理
 * - 环境变量处理
 * - 配置验证
 * - 动态配置更新
 *
 * 预计代码量：1000行
 */

const Joi = require("joi");

class AppConfig {
  constructor() {
    this.config = {};
    this.isInitialized = false;
  }

  /**
   * 初始化配置
   */
  async initialize() {
    try {
      // 加载环境变量
      this.loadEnvironmentVariables();

      // 验证配置
      this.validateConfiguration();

      // 设置默认值
      this.setDefaults();

      this.isInitialized = true;
      console.log("📋 应用配置初始化完成");
    } catch (error) {
      console.error("❌ 配置初始化失败:", error);
      throw error;
    }
  }

  /**
   * 加载环境变量
   */
  loadEnvironmentVariables() {
    this.config = {
      // 服务器配置
      server: {
        port: process.env.PORT || 3000,
        host: process.env.HOST || "localhost",
        nodeEnv: process.env.NODE_ENV || "development",
      },

      // 数据库配置
      database: {
        supabaseUrl: process.env.SUPABASE_URL,
        supabaseKey: process.env.SUPABASE_SERVICE_ROLE_KEY,
        connectionTimeout: parseInt(process.env.DB_CONNECTION_TIMEOUT) || 30000,
        maxConnections: parseInt(process.env.DB_MAX_CONNECTIONS) || 10,
      },

      // AI服务配置
      ai: {
        deepseekApiKey: process.env.DEEPSEEK_API_KEY,
        deepseekEndpoint:
          process.env.LLM_API_ENDPOINT || "https://api.deepseek.com/v1",
        maxTokens: parseInt(process.env.AI_MAX_TOKENS) || 2000,
        temperature: parseFloat(process.env.AI_TEMPERATURE) || 0.7,
        timeout: parseInt(process.env.AI_TIMEOUT) || 30000,
      },

      // 缓存配置
      cache: {
        enabled: process.env.CACHE_ENABLED === "true",
        ttl: parseInt(process.env.CACHE_TTL) || 3600,
        maxSize: parseInt(process.env.CACHE_MAX_SIZE) || 1000,
      },

      // 安全配置
      security: {
        jwtSecret:
          process.env.JWT_SECRET || "default-secret-change-in-production",
        jwtExpiration: process.env.JWT_EXPIRATION || "24h",
        rateLimitWindow: parseInt(process.env.RATE_LIMIT_WINDOW) || 900000, // 15分钟
        rateLimitMax: parseInt(process.env.RATE_LIMIT_MAX) || 100,
        allowedOrigins: process.env.ALLOWED_ORIGINS
          ? process.env.ALLOWED_ORIGINS.split(",")
          : ["http://localhost:3000", "http://localhost:3001"],
      },

      // 性能配置
      performance: {
        requestTimeout: parseInt(process.env.REQUEST_TIMEOUT) || 30000,
        maxRequestSize: process.env.MAX_REQUEST_SIZE || "10mb",
        compressionEnabled: process.env.COMPRESSION_ENABLED !== "false",
      },

      // 日志配置
      logging: {
        level: process.env.LOG_LEVEL || "info",
        format: process.env.LOG_FORMAT || "combined",
        enableConsole: process.env.LOG_CONSOLE !== "false",
        enableFile: process.env.LOG_FILE === "true",
        filePath: process.env.LOG_FILE_PATH || "./logs/app.log",
      },

      // 业务配置
      business: {
        maxRecommendations: parseInt(process.env.MAX_RECOMMENDATIONS) || 10,
        sessionTimeout: parseInt(process.env.SESSION_TIMEOUT_DAYS) || 14, // 14天
        sessionTimeoutMs: parseInt(process.env.SESSION_TIMEOUT) || 1800000, // 30分钟
        maxUploadSize: process.env.MAX_UPLOAD_SIZE || "5mb",
        supportedFileTypes: process.env.SUPPORTED_FILE_TYPES
          ? process.env.SUPPORTED_FILE_TYPES.split(",")
          : ["pdf", "doc", "docx", "txt"],
      },
    };
  }

  /**
   * 验证配置
   */
  validateConfiguration() {
    const schema = Joi.object({
      server: Joi.object({
        port: Joi.number().port().required(),
        host: Joi.string().required(),
        nodeEnv: Joi.string()
          .valid("development", "production", "test")
          .required(),
      }).required(),

      database: Joi.object({
        supabaseUrl: Joi.string().uri().required(),
        supabaseKey: Joi.string().required(),
        connectionTimeout: Joi.number().positive(),
        maxConnections: Joi.number().positive(),
      }).required(),

      ai: Joi.object({
        deepseekApiKey: Joi.string().required(),
        deepseekEndpoint: Joi.string().uri().required(),
        maxTokens: Joi.number().positive(),
        temperature: Joi.number().min(0).max(2),
        timeout: Joi.number().positive(),
      }).required(),

      cache: Joi.object({
        enabled: Joi.boolean(),
        ttl: Joi.number().positive(),
        maxSize: Joi.number().positive(),
      }),

      security: Joi.object({
        jwtSecret: Joi.string().min(32).required(),
        jwtExpiration: Joi.string().required(),
        rateLimitWindow: Joi.number().positive(),
        rateLimitMax: Joi.number().positive(),
        allowedOrigins: Joi.array().items(Joi.string()),
      }).required(),

      performance: Joi.object({
        requestTimeout: Joi.number().positive(),
        maxRequestSize: Joi.string(),
        compressionEnabled: Joi.boolean(),
      }),

      logging: Joi.object({
        level: Joi.string(),
        format: Joi.string(),
        enableConsole: Joi.boolean(),
        enableFile: Joi.boolean(),
        filePath: Joi.string(),
      }),

      business: Joi.object({
        maxRecommendations: Joi.number().positive(),
        sessionTimeout: Joi.number().positive(),
        sessionTimeoutMs: Joi.number().positive(),
        maxUploadSize: Joi.string(),
        supportedFileTypes: Joi.array().items(Joi.string()),
      }),
    });

    const { error } = schema.validate(this.config);
    if (error) {
      throw new Error(`配置验证失败: ${error.details[0].message}`);
    }
  }

  /**
   * 设置默认值
   */
  setDefaults() {
    // 开发环境特殊配置
    if (this.config.server.nodeEnv === "development") {
      this.config.logging.level = "debug";
      this.config.security.rateLimitMax = 1000; // 开发环境放宽限制
    }

    // 生产环境特殊配置
    if (this.config.server.nodeEnv === "production") {
      this.config.logging.enableFile = true;
      this.config.cache.enabled = true;
    }
  }

  // Getter方法
  getServerPort() {
    return this.config.server.port;
  }

  getServerHost() {
    return this.config.server.host;
  }

  getNodeEnv() {
    return this.config.server.nodeEnv;
  }

  getDatabaseConfig() {
    return this.config.database;
  }

  getAIConfig() {
    return this.config.ai;
  }

  getCacheConfig() {
    return this.config.cache;
  }

  getSecurityConfig() {
    return this.config.security;
  }

  getPerformanceConfig() {
    return this.config.performance;
  }

  getLoggingConfig() {
    return this.config.logging;
  }

  getBusinessConfig() {
    return this.config.business;
  }

  getAllowedOrigins() {
    return this.config.security.allowedOrigins;
  }

  /**
   * 获取完整配置
   */
  getFullConfig() {
    return { ...this.config };
  }

  /**
   * 动态更新配置
   */
  updateConfig(path, value) {
    const keys = path.split(".");
    let current = this.config;

    for (let i = 0; i < keys.length - 1; i++) {
      if (!current[keys[i]]) {
        current[keys[i]] = {};
      }
      current = current[keys[i]];
    }

    current[keys[keys.length - 1]] = value;
    console.log(`配置已更新: ${path} = ${value}`);
  }

  /**
   * 检查配置是否已初始化
   */
  isReady() {
    return this.isInitialized;
  }

  // ==================== 容灾回复模板管理 ====================

  /**
   * 获取容灾回复模板
   */
  getFallbackTemplates() {
    return {
      // 基础回复模板
      basic: {
        greeting: [
          "您好！我是AI招聘助手Katrina，很高兴为您服务。",
          "您好！感谢您的关注，我是专注AI领域的猎头Katrina。",
        ],

        infoRequest: [
          "为了给您推荐更合适的职位，能否告诉我您的技术方向、当前公司、职级和期望薪资呢？",
          "请问您的技术栈是什么？目前在哪家公司？这样我能更精准地为您推荐职位。",
        ],

        confirmation: [
          "好的，我了解了。",
          "明白，感谢您的信息。",
          "收到，让我为您查看合适的职位。",
        ],

        jobInquiry: [
          "我们合作了很多优质公司，包括头部大厂、中型公司、创业团队等。需要先了解您的背景才能精准推荐。",
          "职位确实很多，但需要根据您的具体情况来匹配。请问您的技术方向是什么？",
        ],

        decline: [
          "好的，理解您的想法。如果未来有需要，欢迎随时联系我。",
          "没关系，祝您工作顺利！有机会再合作。",
        ],
      },

      // 候选人类型特定模板
      typeSpecific: {
        挤牙膏型: {
          patience: [
            "没关系，您可以慢慢考虑。我这边有很多不错的机会。",
            "理解您需要时间思考，不着急。请问您主要做什么技术方向的？",
            "好的，那我换个问题，您目前的工作年限大概是多少呢？",
          ],
          gentle_push: [
            "为了不浪费您的时间，能简单告诉我您的技术栈吗？",
            "我这边确实有一些不错的机会，但需要了解您的基本情况才能推荐。",
            "您看这样，您先告诉我您的技术方向，我看看有没有合适的？",
          ],
        },

        疯狂咨询型: {
          boundary: [
            "我理解您想了解更多，但我们有规定，需要先了解候选人基本信息才能推荐职位。",
            "抱歉，为了保护双方利益，我需要先了解您的背景再进行推荐。",
            "我们的流程是这样的，先了解您的情况，再匹配合适的职位。",
          ],
          firm: [
            "请您配合提供基本信息，否则我无法为您推荐合适的职位。",
            "如果您不愿意提供基本信息，我可能无法继续为您服务。",
            "我们需要相互配合才能找到最合适的机会。",
          ],
        },

        "质疑/不信任型": {
          trust_building: [
            "我理解您的担心。我们是正规的招聘公司，所有职位都是真实有效的。",
            "我们的职位信息每周都会更新，确保真实性。您可以先了解一下我们公司。",
            "职位的真实性我可以保证，我们与这些公司都有正式的合作协议。",
          ],
          evidence: [
            "我可以提供公司的详细信息和职位描述，您可以先看看是否感兴趣。",
            "如果您担心，我们可以先电话沟通，我详细介绍一下具体情况。",
            "我们公司在行业内有很好的口碑，您可以查询一下我们的资质。",
          ],
        },
      },

      // 紧急情况模板
      emergency: {
        system_error:
          "抱歉，系统暂时有些问题，请稍后再试或者留下联系方式，我稍后回复您。",
        api_limit:
          "当前咨询量较大，请稍等片刻，或者您可以留下微信，我稍后详细为您介绍。",
        unknown_error:
          "抱歉出现了一些技术问题，请问您方便留个联系方式吗？我稍后为您详细介绍。",
      },
    };
  }

  /**
   * 根据场景获取回复模板
   */
  getTemplate(scenario, candidateType = null, context = {}) {
    try {
      const templates = this.getFallbackTemplates();

      // 1. 优先使用候选人类型特定模板
      if (candidateType && templates.typeSpecific[candidateType]) {
        const typeTemplates = templates.typeSpecific[candidateType];
        if (typeTemplates[scenario]) {
          return this.randomSelect(typeTemplates[scenario]);
        }
      }

      // 2. 使用基础模板
      if (templates.basic[scenario]) {
        return this.randomSelect(templates.basic[scenario]);
      }

      // 3. 紧急情况模板
      if (templates.emergency[scenario]) {
        return templates.emergency[scenario];
      }

      // 4. 默认回复
      return this.getDefaultReply(context);
    } catch (error) {
      console.error("❌ 获取模板失败:", error);
      return "抱歉，请稍后再试。";
    }
  }

  /**
   * 随机选择模板
   */
  randomSelect(templates) {
    if (!Array.isArray(templates) || templates.length === 0) {
      return "抱歉，请稍后再试。";
    }
    const index = Math.floor(Math.random() * templates.length);
    return templates[index];
  }

  /**
   * 获取默认回复
   */
  getDefaultReply(context = {}) {
    const { messageCount = 0, hasPersonalInfo = false } = context;

    if (messageCount < 3) {
      return "您好！请问您考虑看看新的工作机会吗？";
    }

    if (!hasPersonalInfo) {
      return "为了给您推荐合适的职位，能否告诉我您的技术方向和期望呢？";
    }

    return "好的，让我为您查看一下合适的职位。";
  }

  /**
   * 智能场景识别
   */
  identifyScenario(userMessage, context = {}) {
    const message = userMessage.toLowerCase();

    // 问候语
    if (/你好|您好|hi|hello/.test(message)) {
      return "greeting";
    }

    // 职位咨询
    if (/职位|工作|推荐|有什么|有哪些/.test(message)) {
      return "jobInquiry";
    }

    // 拒绝
    if (/不需要|不考虑|算了|谢谢|再说/.test(message)) {
      return "decline";
    }

    // 简单确认
    if (/^(嗯|好的|可以|行|是的|对)$/.test(message.trim())) {
      return "confirmation";
    }

    // 信息请求
    if (context.needsInfo) {
      return "infoRequest";
    }

    return "default";
  }

  /**
   * 生成容灾回复
   */
  generateFallbackReply(userMessage, candidateType, context = {}) {
    const scenario = this.identifyScenario(userMessage, context);
    const reply = this.getTemplate(scenario, candidateType, context);

    console.log(`🔄 使用容灾模板: ${scenario} -> ${reply.substring(0, 30)}...`);

    return {
      content: reply,
      source: "fallback_template",
      scenario: scenario,
      candidateType: candidateType,
      timestamp: Date.now(),
    };
  }
}

module.exports = AppConfig;
