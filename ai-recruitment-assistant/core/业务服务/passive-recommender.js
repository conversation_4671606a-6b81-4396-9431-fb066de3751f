/**
 * AI招聘助手系统 - 被动推荐引擎
 *
 * 核心职责：
 * - 4x4 矩阵职位推荐
 * - 候选人档案分析
 * - 推荐算法实现
 * - 推荐缓存管理
 *
 * 预计代码量：1800行
 */

class PassiveRecommender {
  constructor(database, config) {
    this.database = database;
    this.config = config;
    this.isInitialized = false;

    // 4x4推荐规则配置（4种公司类型各推荐1个职位）
    this.companyTypes = {
      A: "头部大厂",
      B: "国企",
      C: "中型公司",
      D: "创业型公司",
    };

    // 4x4推荐规则：根据缺失/排除的公司类型调整推荐
    this.recommendationRules = {
      default: ["A", "B", "C", "D"],
      exclude_A: ["B", "C", "D", "B"],
      exclude_B: ["A", "C", "D", "A"],
      exclude_AB: ["C", "C", "C", "D"],
      exclude_ABC: ["D", "D", "D", "D"],
    };
  }

  /**
   * 初始化被动推荐引擎
   */
  async initialize() {
    try {
      // 加载推荐算法配置
      await this.loadRecommendationConfig();

      this.isInitialized = true;
      console.log("🎯 被动推荐引擎初始化完成");
    } catch (error) {
      console.error("❌ 被动推荐引擎初始化失败:", error);
      throw error;
    }
  }

  /**
   * 加载推荐配置
   */
  async loadRecommendationConfig() {
    // 加载推荐算法的各种配置参数
    this.recommendationConfig = {
      maxRecommendations: 4, // 4x4推荐固定为4个
      scoreThreshold: 0.6,
      diversityFactor: 0.3,
    };
  }

  /**
   * 生成4x4推荐（4种公司类型各1个职位）
   */
  async generate4x4Recommendations(
    userId,
    excludedTypes = [],
    targetType = null
  ) {
    try {
      // 获取用户档案
      const profile = await this.database.getCandidateProfile(userId);
      if (!profile) {
        throw new Error("用户档案不存在");
      }

      // 确定推荐规则
      const recommendationPattern = this.determineRecommendationPattern(
        excludedTypes,
        targetType
      );

      // 生成推荐职位
      const recommendations = await this.generateRecommendationsByPattern(
        profile,
        recommendationPattern
      );

      return {
        success: true,
        recommendations: recommendations,
        pattern: recommendationPattern,
        excludedTypes: excludedTypes,
        targetType: targetType,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error("❌ 生成4x4推荐失败:", error);
      throw error;
    }
  }

  /**
   * 确定推荐模式
   */
  determineRecommendationPattern(excludedTypes, targetType) {
    // 如果指定了目标类型（被动推荐），全部推荐该类型
    if (targetType) {
      return [targetType, targetType, targetType, targetType];
    }

    // 根据排除的类型确定推荐模式
    const excludeKey = this.getExcludeKey(excludedTypes);
    return (
      this.recommendationRules[excludeKey] || this.recommendationRules.default
    );
  }

  /**
   * 获取排除规则键名
   */
  getExcludeKey(excludedTypes) {
    if (excludedTypes.length === 0) return "default";

    const sortedExcludes = excludedTypes.sort();
    if (sortedExcludes.length === 1) {
      return `exclude_${sortedExcludes[0]}`;
    } else if (
      sortedExcludes.length === 2 &&
      sortedExcludes.includes("A") &&
      sortedExcludes.includes("B")
    ) {
      return "exclude_AB";
    } else if (
      sortedExcludes.length === 3 &&
      sortedExcludes.includes("A") &&
      sortedExcludes.includes("B") &&
      sortedExcludes.includes("C")
    ) {
      return "exclude_ABC";
    }

    return "default";
  }

  /**
   * 根据模式生成推荐
   */
  async generateRecommendationsByPattern(profile, pattern) {
    const recommendations = [];

    for (let i = 0; i < pattern.length; i++) {
      const companyTypeKey = pattern[i];
      const companyTypeName = this.companyTypes[companyTypeKey];

      // 查找该公司类型的职位
      const job = await this.findJobForCompanyType(profile, companyTypeName);

      if (job) {
        recommendations.push({
          position: i + 1,
          companyType: companyTypeName,
          companyTypeKey: companyTypeKey,
          job: job,
          matchScore: this.calculateJobMatchScore(profile, job),
        });
      }
    }

    return recommendations;
  }

  /**
   * 为特定公司类型查找职位
   */
  async findJobForCompanyType(profile, companyTypeName) {
    try {
      // 构建搜索条件
      const criteria = {
        limit: 10, // 多查一些，然后筛选最佳匹配
      };

      // 技术方向匹配
      if (profile.primary_tech_direction_id) {
        criteria.techDirectionId = profile.primary_tech_direction_id;
      }

      // 薪资范围匹配
      if (profile.expected_compensation_min) {
        criteria.salaryMin = profile.expected_compensation_min;
      }
      if (profile.expected_compensation_max) {
        criteria.salaryMax = profile.expected_compensation_max;
      }

      // 经验等级匹配
      if (profile.candidate_standard_level_min) {
        criteria.experienceMin = profile.candidate_standard_level_min;
      }
      if (profile.candidate_standard_level_max) {
        criteria.experienceMax = profile.candidate_standard_level_max;
      }

      // 使用数据库管理器搜索职位
      const jobs = await this.database.searchJobs(criteria);

      // 根据公司类型筛选
      const filteredJobs = jobs.filter((job) => {
        return job.companies && job.companies.company_type === companyTypeName;
      });

      // 返回最佳匹配的职位
      if (filteredJobs.length > 0) {
        // 按匹配度排序，返回最佳匹配
        filteredJobs.sort((a, b) => {
          const scoreA = this.calculateJobMatchScore(profile, a);
          const scoreB = this.calculateJobMatchScore(profile, b);
          return scoreB - scoreA;
        });
        return filteredJobs[0];
      }

      return null;
    } catch (error) {
      console.error("❌ 查找公司类型职位失败:", error);
      return null;
    }
  }

  /**
   * 计算职位匹配分数
   */
  calculateJobMatchScore(profile, job) {
    try {
      let score = 70; // 基础分数

      // 技术方向匹配
      if (
        profile.primary_tech_direction_id &&
        job.primary_tech_direction_id === profile.primary_tech_direction_id
      ) {
        score += 15;
      }

      // 薪资匹配
      if (
        profile.expected_compensation_min &&
        profile.expected_compensation_max
      ) {
        const jobSalaryMin = job.salary_min || 0;
        const jobSalaryMax = job.salary_max || 999999;
        const profileMin = profile.expected_compensation_min;
        const profileMax = profile.expected_compensation_max;

        // 检查薪资范围重叠
        if (jobSalaryMax >= profileMin && jobSalaryMin <= profileMax) {
          score += 10;
        }
      }

      // 职级匹配
      if (profile.candidate_standard_level_min && job.job_standard_level_min) {
        const levelDiff = Math.abs(
          profile.candidate_standard_level_min - job.job_standard_level_min
        );
        if (levelDiff <= 1) {
          score += 10;
        } else if (levelDiff <= 2) {
          score += 5;
        }
      }

      // 职位信息完整度
      if (job.job_description && job.job_description.length > 100) {
        score += 3;
      }
      if (job.work_location && job.work_location !== "地点待定") {
        score += 2;
      }

      return Math.min(Math.max(score, 60), 95);
    } catch (error) {
      console.error("❌ 计算职位匹配度失败:", error);
      return 75;
    }
  }

  /**
   * 计算格子匹配分数
   */
  calculateCellMatchScore(profile, companyType, techDirection) {
    // 基础匹配分数计算逻辑
    let score = 0.5; // 基础分数

    // 技术方向匹配
    if (profile.primary_tech_direction_id) {
      // 这里需要实现技术方向匹配逻辑
      score += 0.3;
    }

    // 公司类型偏好匹配
    // 这里需要实现公司类型偏好匹配逻辑
    score += 0.2;

    return Math.min(score, 1.0);
  }

  /**
   * 检查引擎状态
   */
  isReady() {
    return this.isInitialized;
  }

  // ==================== 职位格式化模块 ====================

  /**
   * 格式化职位推荐内容（优化版：AI摘要 + 简洁展示）
   */
  async formatJobRecommendations(jobsByCompanyType, aiServices) {
    try {
      if (!jobsByCompanyType || Object.keys(jobsByCompanyType).length === 0) {
        return null;
      }

      let content =
        "我刚刚快速的查询了一下，先给你推荐几个职位，你看看有没有感兴趣的。\n\n";
      let jobCount = 1;

      // 处理每个职位，生成AI摘要
      for (const [companyType, jobs] of Object.entries(jobsByCompanyType)) {
        for (const job of jobs) {
          // 安全获取公司名称
          const companyName = job.companies?.company_name || "未知公司";

          // 生成职位摘要
          const jobSummary = await this.generateJobSummary(job, aiServices);

          // 计算匹配度
          const matchScore = this.calculateMatchScore(job);

          // 修复职级显示逻辑
          let levelDisplay;
          if (
            job.job_level_raw &&
            job.job_level_raw.trim() &&
            job.job_level_raw !== "无"
          ) {
            levelDisplay = job.job_level_raw;
          } else if (job.job_standard_level_min && job.job_standard_level_max) {
            levelDisplay = `对标P${job.job_standard_level_min}-P${job.job_standard_level_max}`;
          } else if (job.job_standard_level_min) {
            levelDisplay = `对标P${job.job_standard_level_min}+`;
          } else {
            levelDisplay = "职级面议";
          }

          content += `${jobCount}. 🏢 ${companyName} - ${job.job_title || "未知职位"}\n`;
          content += `💼 ${levelDisplay} 📍 ${job.work_location || "地点待定"}\n`;
          content += `🎯 ${jobSummary}\n`;
          content += `⭐ 匹配度：${matchScore}%\n`;
          content += `回复"详情${jobCount}"查看完整职位信息\n\n`;
          jobCount++;
        }
      }

      content +=
        "您手头上有简历吗？麻烦您发一下给我，这样能够给您匹配更加精准的职位。";

      return content;
    } catch (error) {
      console.error("❌ 格式化职位推荐失败:", error);
      // 降级到原始格式
      return this.formatJobRecommendationsFallback(jobsByCompanyType);
    }
  }

  /**
   * 生成职位智能摘要
   */
  async generateJobSummary(job, aiServices) {
    try {
      const jobDescription = job.job_description || job.requirements || "";

      if (!jobDescription || jobDescription.length < 20) {
        return "详细要求请咨询";
      }

      // 构建AI摘要提示词
      const prompt = `请将以下职位描述总结为一句话的核心要点（不超过50字）：

职位描述：
${jobDescription}

要求：
1. 提取最核心的技术要求和职责
2. 突出亮点和特色
3. 语言简洁有力
4. 不超过50字

格式：直接输出摘要内容，不要其他说明`;

      // 调用AI服务生成摘要
      const summary = await aiServices.generateResponse(prompt, {
        maxTokens: 100,
        temperature: 0.3,
      });

      // 清理和验证摘要
      const cleanSummary = summary.trim().replace(/^["']|["']$/g, "");

      if (cleanSummary.length > 80) {
        return cleanSummary.substring(0, 77) + "...";
      }

      return cleanSummary || "详细要求请咨询";
    } catch (error) {
      console.error("❌ 生成职位摘要失败:", error);
      return "详细要求请咨询";
    }
  }

  /**
   * 计算职位匹配度
   */
  calculateMatchScore(job) {
    try {
      let score = 70; // 基础分数

      // 根据职位信息完整度调整分数
      if (job.job_description && job.job_description.length > 100) {
        score += 5;
      }

      if (job.job_level_raw && job.job_level_raw !== "无") {
        score += 5;
      }

      if (job.work_location && job.work_location !== "地点待定") {
        score += 5;
      }

      if (job.companies?.company_name) {
        score += 5;
      }

      // 确保分数在合理范围内
      return Math.min(Math.max(score, 60), 95);
    } catch (error) {
      console.error("❌ 计算匹配度失败:", error);
      return 75; // 默认分数
    }
  }

  /**
   * 降级格式化（原始版本，作为备用）
   */
  formatJobRecommendationsFallback(jobsByCompanyType) {
    try {
      let content =
        "我刚刚快速的查询了一下，先给你推荐几个职位，你看看有没有感兴趣的。\n\n";
      let jobCount = 1;

      Object.entries(jobsByCompanyType).forEach(([companyType, jobs]) => {
        jobs.forEach((job) => {
          const companyName = job.companies?.company_name || "未知公司";

          content += `公司${jobCount}：${companyName}\n`;
          content += `职位：${job.job_title || "未知职位"}\n`;
          content += `职级：${job.job_level_raw || `对标P${job.job_standard_level_min || "?"}-P${job.job_standard_level_max || "?"}`}\n`;
          content += `职位要点：详细要求请咨询\n\n`;
          jobCount++;
        });
      });

      content +=
        "您手头上有简历吗？麻烦您发一下给我，这样能够给您匹配更加精准的职位。";

      return content;
    } catch (error) {
      console.error("❌ 降级格式化失败:", error);
      return "抱歉，职位信息处理出现问题，请稍后再试。";
    }
  }
}

module.exports = PassiveRecommender;
