/**
 * AI招聘助手系统 - 用户管理器
 *
 * 核心职责：
 * - 用户认证和授权
 * - 会话管理
 * - 用户档案维护
 * - 权限控制
 *
 * 预计代码量：1200行
 */

class UserManager {
  constructor(database, config) {
    this.database = database;
    this.config = config;
    this.isInitialized = false;
  }

  /**
   * 初始化用户管理器
   */
  async initialize() {
    try {
      this.isInitialized = true;
      console.log("👤 用户管理器初始化完成");
    } catch (error) {
      console.error("❌ 用户管理器初始化失败:", error);
      throw error;
    }
  }

  /**
   * 获取或创建用户
   */
  async getOrCreateUser(email) {
    try {
      // 尝试获取现有用户
      let user = await this.database.getUserByEmail(email);

      if (!user) {
        // 创建新用户
        user = await this.database.createUser({
          email: email,
          userType: "candidate",
        });

        console.log("✅ 新用户创建成功:", email);
      } else {
        // 更新最后登录时间
        await this.database.updateUserLastLogin(user.id);
      }

      return user;
    } catch (error) {
      console.error("❌ 获取或创建用户失败:", error);
      throw error;
    }
  }

  /**
   * 根据会话获取用户
   */
  async getUserBySession(session) {
    try {
      const { data, error } = await this.database.client
        .from("users")
        .select("*")
        .eq("id", session.user_id)
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error("❌ 根据会话获取用户失败:", error);
      throw error;
    }
  }

  /**
   * 验证用户权限
   */
  async validateUserPermission(userId, permission) {
    try {
      // 基础权限验证逻辑
      // 这里可以扩展为更复杂的权限系统
      return true;
    } catch (error) {
      console.error("❌ 验证用户权限失败:", error);
      return false;
    }
  }

  /**
   * 获取用户统计信息
   */
  async getUserStats(userId) {
    try {
      // 获取用户的各种统计信息
      // 如：会话数量、消息数量、推荐数量等
      return {
        totalSessions: 0,
        totalMessages: 0,
        totalRecommendations: 0,
      };
    } catch (error) {
      console.error("❌ 获取用户统计信息失败:", error);
      return {};
    }
  }

  // ==================== 用户信息状态管理 ====================

  /**
   * 获取用户信息收集状态
   */
  async getUserInfoState(userId) {
    try {
      const profile = await this.database.getCandidateProfile(userId);

      if (!profile) {
        return {
          所在公司: null,
          技术方向: null,
          当前职级: null,
          期望薪资: null,
          所在城市: null,
          业务场景: null,
          收集进度: 0,
          可推荐: false,
        };
      }

      const infoState = {
        所在公司: profile.current_company_name_raw || null,
        技术方向: profile.candidate_tech_direction_raw || null,
        当前职级: profile.candidate_level_raw || null,
        期望薪资: profile.expected_compensation_raw || null,
        所在城市: profile.desired_location_raw || null,
        业务场景: profile.candidate_business_scenario_raw || null,
      };

      // 计算收集进度
      const collectedCount = Object.values(infoState).filter(
        (v) => v !== null
      ).length;
      infoState.收集进度 = collectedCount;

      // 判断是否可以推荐（4*4逻辑的触发条件）
      infoState.可推荐 = this.checkRecommendationEligibility(infoState);

      return infoState;
    } catch (error) {
      console.error("❌ 获取用户信息状态失败:", error);
      return null;
    }
  }

  /**
   * 更新用户信息状态
   */
  async updateUserInfoState(userId, infoUpdates) {
    try {
      // 使用upsert方法创建或更新档案
      const updates = this.mapInfoToProfile(infoUpdates);
      const profile = await this.database.upsertCandidateProfile(
        userId,
        updates
      );

      console.log(
        `✅ 用户信息状态更新成功: ${Object.keys(infoUpdates).join(", ")}`
      );

      // 返回更新后的状态
      return await this.getUserInfoState(userId);
    } catch (error) {
      console.error("❌ 更新用户信息状态失败:", error);
      throw error;
    }
  }

  /**
   * 检查推荐资格（4*4逻辑触发条件）
   */
  checkRecommendationEligibility(infoState) {
    const { 所在公司, 技术方向, 当前职级, 期望薪资 } = infoState;

    // 条件1：所在公司 + 技术方向 + 当前职级
    if (所在公司 && 技术方向 && 当前职级) {
      return true;
    }

    // 条件2：所在公司 + 技术方向 + 期望薪资
    if (所在公司 && 技术方向 && 期望薪资) {
      return true;
    }

    // 条件3：所在公司 + 技术方向 + 期望薪资 + 当前职级
    if (所在公司 && 技术方向 && 期望薪资 && 当前职级) {
      return true;
    }

    return false;
  }

  /**
   * 检查精准推荐资格（第二次推荐条件）
   */
  checkPreciseRecommendationEligibility(infoState) {
    const { 所在城市, 业务场景 } = infoState;

    // 需要在基础推荐条件基础上，增加城市和业务场景
    return (
      this.checkRecommendationEligibility(infoState) && 所在城市 && 业务场景
    );
  }

  /**
   * 映射用户信息到数据库字段
   */
  mapInfoToProfile(infoUpdates) {
    const mapping = {
      所在公司: "current_company_name_raw",
      技术方向: "candidate_tech_direction_raw",
      当前职级: "candidate_level_raw",
      期望薪资: "expected_compensation_raw",
      所在城市: "desired_location_raw",
      业务场景: "candidate_business_scenario_raw",
    };

    const profileUpdates = {};
    Object.entries(infoUpdates).forEach(([key, value]) => {
      if (mapping[key]) {
        profileUpdates[mapping[key]] = value;
      }
    });

    return profileUpdates;
  }

  /**
   * 获取缺失的信息项
   */
  getMissingInfo(infoState) {
    const required = ["所在公司", "技术方向", "当前职级", "期望薪资"];
    const optional = ["所在城市", "业务场景"];

    const missingRequired = required.filter((key) => !infoState[key]);
    const missingOptional = optional.filter((key) => !infoState[key]);

    return {
      required: missingRequired,
      optional: missingOptional,
      nextToCollect: missingRequired[0] || missingOptional[0],
    };
  }

  /**
   * 检查管理器状态
   */
  isReady() {
    return this.isInitialized;
  }

  // ==================== 信息提取模块 ====================

  /**
   * 从用户消息中提取信息
   */
  async extractUserInfo(message) {
    try {
      const extractedInfo = {};

      // 提取技术方向
      const techDirection = this.extractTechDirection(message);
      if (techDirection) {
        extractedInfo.技术方向 = techDirection;
      }

      // 提取公司信息
      const company = this.extractCompanyFromMessage(message);
      if (company) {
        extractedInfo.所在公司 = company;
      }

      // 提取职级信息
      const level = this.extractLevelFromMessage(message);
      if (level) {
        extractedInfo.当前职级 = level;
      }

      // 提取薪资信息
      const salary = this.extractSalaryFromMessage(message);
      if (salary) {
        extractedInfo.期望薪资 = salary;
      }

      // 提取城市信息
      const city = this.extractCityFromMessage(message);
      if (city) {
        extractedInfo.所在城市 = city;
      }

      // 提取业务场景
      const businessScenario = this.extractBusinessScenario(message);
      if (businessScenario) {
        extractedInfo.业务场景 = businessScenario;
      }

      console.log("📝 解析到用户信息:", extractedInfo);
      return extractedInfo;
    } catch (error) {
      console.error("❌ 提取用户信息失败:", error);
      return {};
    }
  }

  /**
   * 提取技术方向
   */
  extractTechDirection(message) {
    const techKeywords = {
      推荐算法: ["推荐算法", "推荐系统", "个性化推荐", "协同过滤", "推荐引擎"],
      搜索算法: ["搜索算法", "搜索引擎", "信息检索", "搜索排序", "查询理解"],
      "CV算法（计算机视觉）": [
        "计算机视觉",
        "CV",
        "图像识别",
        "目标检测",
        "人脸识别",
        "图像处理",
      ],
      "NLP算法（自然语言处理）": [
        "自然语言处理",
        "NLP",
        "文本分析",
        "语言模型",
        "机器翻译",
        "文本挖掘",
      ],
      "大模型（LLM）算法": [
        "大模型",
        "LLM",
        "GPT",
        "BERT",
        "Transformer",
        "预训练模型",
      ],
      多模态算法: ["多模态", "视觉语言", "图文理解", "跨模态"],
      "通用机器学习/深度学习算法": [
        "机器学习",
        "深度学习",
        "神经网络",
        "算法工程师",
        "AI算法",
      ],
    };

    for (const [tech, keywords] of Object.entries(techKeywords)) {
      if (keywords.some((keyword) => message.includes(keyword))) {
        return tech;
      }
    }

    return null;
  }

  /**
   * 提取公司信息
   */
  extractCompanyFromMessage(message) {
    const companies = [
      "腾讯",
      "阿里巴巴",
      "阿里",
      "百度",
      "字节跳动",
      "美团",
      "京东",
      "滴滴",
      "小米",
      "华为",
      "OPPO",
      "vivo",
      "网易",
      "新浪",
      "搜狐",
      "360",
      "快手",
      "拼多多",
      "蚂蚁金服",
      "蚂蚁",
      "饿了么",
      "高德",
      "钉钉",
      "微软",
      "谷歌",
      "苹果",
      "Facebook",
      "亚马逊",
      "特斯拉",
    ];

    for (const company of companies) {
      if (message.includes(company)) {
        return company;
      }
    }

    // 检查是否有"在...工作"的模式
    const workPattern = /在(.{2,10}?)工作/;
    const workMatch = message.match(workPattern);
    if (workMatch) {
      return workMatch[1];
    }

    return null;
  }

  /**
   * 提取职级信息
   */
  extractLevelFromMessage(message) {
    // P级别
    const pLevelPattern = /P(\d+)/i;
    const pMatch = message.match(pLevelPattern);
    if (pMatch) {
      return `P${pMatch[1]}`;
    }

    // T级别
    const tLevelPattern = /T(\d+)/i;
    const tMatch = message.match(tLevelPattern);
    if (tMatch) {
      return `T${tMatch[1]}`;
    }

    // 数字级别（如"10级"）
    const numLevelPattern = /(\d+)级/;
    const numMatch = message.match(numLevelPattern);
    if (numMatch) {
      return `${numMatch[1]}级`;
    }

    // 职位级别关键词
    const levelKeywords = {
      初级: ["初级", "junior", "Junior"],
      中级: ["中级", "middle", "Middle"],
      高级: ["高级", "senior", "Senior"],
      专家: ["专家", "expert", "Expert"],
      资深: ["资深", "principal", "Principal"],
      架构师: ["架构师", "architect", "Architect"],
    };

    for (const [level, keywords] of Object.entries(levelKeywords)) {
      if (
        keywords.some((keyword) =>
          message.toLowerCase().includes(keyword.toLowerCase())
        )
      ) {
        return level;
      }
    }

    return null;
  }

  /**
   * 提取薪资信息
   */
  extractSalaryFromMessage(message) {
    // 匹配各种薪资格式
    const salaryPatterns = [
      /(\d+)万/,
      /(\d+)k/i,
      /(\d+)K/,
      /薪资(\d+)/,
      /期望(\d+)/,
      /(\d+)左右/,
    ];

    for (const pattern of salaryPatterns) {
      const match = message.match(pattern);
      if (match) {
        const amount = parseInt(match[1]);
        if (amount > 1000) {
          // 如果是K格式，转换为万
          return `${Math.round(amount / 10)}万`;
        } else {
          return `${amount}万`;
        }
      }
    }

    return null;
  }

  /**
   * 提取城市信息
   */
  extractCityFromMessage(message) {
    const cities = [
      "北京",
      "上海",
      "深圳",
      "广州",
      "杭州",
      "成都",
      "武汉",
      "西安",
      "南京",
      "苏州",
      "天津",
      "重庆",
      "青岛",
      "大连",
      "厦门",
      "长沙",
      "郑州",
      "济南",
      "沈阳",
      "合肥",
      "福州",
      "昆明",
      "石家庄",
      "太原",
    ];

    for (const city of cities) {
      if (message.includes(city)) {
        return city;
      }
    }

    return null;
  }

  /**
   * 提取业务场景
   */
  extractBusinessScenario(message) {
    const scenarios = {
      电商: ["电商", "购物", "商城", "零售"],
      社交: ["社交", "聊天", "社区", "朋友圈"],
      视频: ["视频", "直播", "短视频", "影音"],
      游戏: ["游戏", "娱乐", "竞技"],
      金融: ["金融", "支付", "理财", "银行"],
      出行: ["出行", "打车", "地图", "导航"],
      教育: ["教育", "学习", "培训", "课程"],
      医疗: ["医疗", "健康", "医院", "诊断"],
    };

    for (const [scenario, keywords] of Object.entries(scenarios)) {
      if (keywords.some((keyword) => message.includes(keyword))) {
        return scenario;
      }
    }

    return null;
  }

  // ==================== 意图识别模块 ====================

  /**
   * 初始化意图识别服务
   */
  initializeIntentRecognizer(aiServices, utilities, database) {
    this.aiServices = aiServices;
    this.utilities = utilities;
    this.database = database;

    // 意图类型定义
    this.intentTypes = {
      GREETING: "greeting",
      PROFILE_UPDATE: "profile_update",
      JOB_SEARCH: "job_search",
      RECOMMENDATION_REQUEST: "recommendation_request",
      TECH_DIRECTION_INQUIRY: "tech_direction_inquiry",
      SALARY_INQUIRY: "salary_inquiry",
      COMPANY_INQUIRY: "company_inquiry",
      LOCATION_INQUIRY: "location_inquiry",
      EXPERIENCE_INQUIRY: "experience_inquiry",
      RESUME_UPLOAD: "resume_upload",
      JOB_DETAIL_REQUEST: "job_detail_request",
      UNKNOWN: "unknown",
    };
  }

  /**
   * 主要意图分析入口（集成候选人类型分析）
   */
  async analyzeUserIntent(message, session, isFirstUserMessage = null) {
    try {
      if (isFirstUserMessage) {
        // 第一句回复使用三层处理逻辑
        return await this.analyzeFirstUserMessage(message, session);
      }

      // 1. 获取对话历史
      const messageHistory = await this.getRecentMessages(session.id, 5);

      // 2. 候选人类型分析（智能触发）
      let candidateType = null;
      if (this.shouldAnalyzeCandidateType(session, messageHistory.length)) {
        candidateType = await this.aiServices.analyzeCandidateType(
          messageHistory,
          session.session_uuid
        );

        // 更新会话中的候选人类型信息
        await this.updateSessionCandidateType(session.id, candidateType);
      }

      // 3. 使用AI服务分析意图
      const aiAnalysis = await this.aiServices.analyzeUserIntent(message, {
        sessionContext: session.current_interaction_context,
        messageHistory: messageHistory,
        candidateType: candidateType,
      });

      // 4. 结合规则引擎进行意图识别
      const ruleBasedIntent = this.identifyIntentByRules(message);

      // 5. 合并分析结果
      const finalIntent = this.mergeIntentAnalysis(aiAnalysis, ruleBasedIntent);

      return {
        type: finalIntent.type,
        confidence: finalIntent.confidence,
        entities: finalIntent.entities || {},
        context: finalIntent.context || {},
        candidateType: candidateType,
      };
    } catch (error) {
      console.error("❌ 意图分析失败:", error);

      // 回退到规则引擎
      return this.identifyIntentByRules(message);
    }
  }

  /**
   * 分析第一句用户消息（三层处理逻辑）
   */
  async analyzeFirstUserMessage(message, _session) {
    try {
      // 第一层：关键词快速匹配
      const quickMatch = this.checkQuickPatterns(message);
      if (quickMatch) {
        return {
          type: quickMatch.type,
          confidence: 0.9,
          source: "quick_pattern",
          needsFollowUp: true,
          extractedInfo: {},
          messageCategory: quickMatch.category,
        };
      }

      // 第二层：信息提取处理
      const extractedInfo = await this.extractUserInfo(message);
      if (extractedInfo.hasInfo) {
        return {
          type: this.intentTypes.PROFILE_UPDATE,
          confidence: 0.8,
          source: "info_extraction",
          needsFollowUp: true,
          extractedInfo: extractedInfo,
          messageCategory: "info_providing",
        };
      }

      // 第三层：AI意图分析
      const aiIntent = await this.aiServices.analyzeUserIntent(message, {
        isFirstMessage: true,
        context: "user_greeting_or_inquiry",
      });

      return {
        type: aiIntent.type || this.intentTypes.UNKNOWN,
        confidence: aiIntent.confidence || 0.5,
        source: "ai_analysis",
        needsFollowUp: true,
        extractedInfo: {},
        messageCategory: aiIntent.category || "general_inquiry",
      };
    } catch (error) {
      console.error("❌ 第一句消息意图分析失败:", error);
      return {
        type: this.intentTypes.UNKNOWN,
        confidence: 0.3,
        source: "fallback",
        needsFollowUp: true,
        extractedInfo: {},
        messageCategory: "unknown",
      };
    }
  }

  /**
   * 快速模式匹配（第一层）
   */
  checkQuickPatterns(message) {
    const lowerMessage = message.toLowerCase().trim();

    // 职位询问的精确匹配
    const jobInquiryPatterns = [
      "有什么职位",
      "有什么岗位",
      "有什么工作",
      "职位推荐",
      "岗位推荐",
      "工作推荐",
      "找工作",
      "求职",
    ];

    if (jobInquiryPatterns.some((pattern) => lowerMessage.includes(pattern))) {
      return {
        type: this.intentTypes.JOB_SEARCH,
        category: "job_inquiry",
      };
    }

    // 问候语的精确匹配
    const greetingPatterns = ["你好", "hello", "嗨", "hi"];
    if (greetingPatterns.some((pattern) => lowerMessage.includes(pattern))) {
      return {
        type: this.intentTypes.GREETING,
        category: "greeting",
      };
    }

    return null;
  }

  /**
   * 基于规则的意图识别
   */
  identifyIntentByRules(message) {
    const lowerMessage = message.toLowerCase();

    // 职位详情查询识别（优先级最高）
    const detailMatch = lowerMessage.match(/详情(\d+)/);
    if (detailMatch) {
      return {
        type: this.intentTypes.JOB_DETAIL_REQUEST,
        confidence: 0.95,
        entities: {
          jobIndex: parseInt(detailMatch[1]),
        },
      };
    }

    // 问候语识别
    if (
      this.utilities.containsAny(lowerMessage, ["你好", "hello", "嗨", "开始"])
    ) {
      return {
        type: this.intentTypes.GREETING,
        confidence: 0.9,
        entities: {},
      };
    }

    // 职位搜索识别 - 优先级更高，包含职位相关的推荐
    if (
      this.utilities.containsAny(lowerMessage, [
        "找工作",
        "职位",
        "岗位",
        "招聘",
        "职位推荐",
        "岗位推荐",
        "工作推荐",
        "有什么职位",
        "有什么岗位",
        "有什么工作",
      ])
    ) {
      return {
        type: this.intentTypes.JOB_SEARCH,
        confidence: 0.9,
        entities: {},
      };
    }

    // 推荐请求识别 - 非职位相关的推荐
    if (this.utilities.containsAny(lowerMessage, ["推荐", "建议", "合适的"])) {
      return {
        type: this.intentTypes.RECOMMENDATION_REQUEST,
        confidence: 0.7,
        entities: {},
      };
    }

    // 技术方向询问
    if (
      this.utilities.containsAny(lowerMessage, [
        "技术",
        "开发",
        "前端",
        "后端",
        "算法",
      ])
    ) {
      return {
        type: this.intentTypes.TECH_DIRECTION_INQUIRY,
        confidence: 0.7,
        entities: {},
      };
    }

    // 薪资询问
    if (
      this.utilities.containsAny(lowerMessage, [
        "薪资",
        "工资",
        "薪水",
        "待遇",
        "收入",
      ])
    ) {
      return {
        type: this.intentTypes.SALARY_INQUIRY,
        confidence: 0.8,
        entities: {},
      };
    }

    // 默认未知意图
    return {
      type: this.intentTypes.UNKNOWN,
      confidence: 0.3,
      entities: {},
    };
  }

  /**
   * 合并AI分析和规则分析的结果
   */
  mergeIntentAnalysis(aiAnalysis, ruleBasedIntent) {
    // 如果规则引擎有高置信度结果，优先使用
    if (ruleBasedIntent.confidence >= 0.8) {
      return ruleBasedIntent;
    }

    // 如果AI分析有高置信度结果，使用AI结果
    if (aiAnalysis.confidence >= 0.7) {
      return aiAnalysis;
    }

    // 否则选择置信度更高的
    return ruleBasedIntent.confidence >= aiAnalysis.confidence
      ? ruleBasedIntent
      : aiAnalysis;
  }

  /**
   * 获取最近的消息历史（用于AI分析上下文）
   */
  async getRecentMessages(sessionId, limit = 5) {
    if (!this.database) {
      return [];
    }

    try {
      return await this.database.getSessionMessages(sessionId, limit);
    } catch (error) {
      console.error("❌ 获取消息历史失败:", error);
      return [];
    }
  }

  // ==================== 候选人类型分析模块 ====================

  /**
   * 智能判断是否需要分析候选人类型
   */
  shouldAnalyzeCandidateType(session, messageHistory) {
    const messageCount = messageHistory.length;
    const lastType = session.current_interaction_context?.candidateType;

    // 1. 首次分析或无历史类型
    if (!lastType || messageCount <= 2) return true;

    // 2. 基于对话轮次的动态策略
    const cacheExpiry = this.getCacheExpiryByStage(messageCount);
    const timeSinceLastAnalysis = Date.now() - (lastType.timestamp || 0);
    if (timeSinceLastAnalysis > cacheExpiry) return true;

    // 3. 检测行为模式变化
    if (this.detectBehaviorChange(messageHistory, lastType)) return true;

    // 4. 关键信息收集节点
    if (this.isKeyInformationMoment(messageHistory)) return true;

    // 5. 置信度过低需要重新分析
    if (lastType.confidence < 0.6) return true;

    return false;
  }

  /**
   * 根据对话阶段获取缓存过期时间
   */
  getCacheExpiryByStage(messageCount) {
    if (messageCount <= 5) return 2 * 60 * 60 * 1000; // 2小时
    if (messageCount <= 10) return 6 * 60 * 60 * 1000; // 6小时
    return 24 * 60 * 60 * 1000; // 24小时
  }

  /**
   * 检测候选人行为模式变化
   */
  detectBehaviorChange(messageHistory, lastType) {
    if (messageHistory.length < 4) return false;

    const recentMessages = messageHistory.slice(-3);
    const userMessages = recentMessages.filter((msg) => msg.role === "user");

    // 计算最近行为特征
    const recentBehavior = this.analyzeBehaviorPattern(userMessages);

    // 与上次分析的行为模式对比
    const lastBehavior = lastType.behaviorPattern || {};

    // 计算行为变化程度
    const changeScore = this.calculateBehaviorChangeScore(
      recentBehavior,
      lastBehavior
    );

    return changeScore > 0.3; // 变化超过30%时重新分析
  }

  /**
   * 分析行为模式
   */
  analyzeBehaviorPattern(userMessages) {
    if (userMessages.length === 0) return {};

    const avgLength =
      userMessages.reduce((sum, msg) => sum + msg.content.length, 0) /
      userMessages.length;
    const questionCount = userMessages.filter((msg) =>
      /[?？]/.test(msg.content)
    ).length;
    const shortReplyCount = userMessages.filter(
      (msg) => msg.content.length < 5
    ).length;
    const infoProvided = userMessages.filter((msg) =>
      this.containsPersonalInfo(msg.content)
    ).length;

    return {
      avgMessageLength: avgLength,
      questionRatio: questionCount / userMessages.length,
      shortReplyRatio: shortReplyCount / userMessages.length,
      infoProvisionRatio: infoProvided / userMessages.length,
    };
  }

  /**
   * 计算行为变化分数
   */
  calculateBehaviorChangeScore(current, previous) {
    const metrics = [
      "avgMessageLength",
      "questionRatio",
      "shortReplyRatio",
      "infoProvisionRatio",
    ];
    let totalChange = 0;
    let validMetrics = 0;

    for (const metric of metrics) {
      if (current[metric] !== undefined && previous[metric] !== undefined) {
        const change =
          Math.abs(current[metric] - previous[metric]) /
          (previous[metric] + 0.1);
        totalChange += change;
        validMetrics++;
      }
    }

    return validMetrics > 0 ? totalChange / validMetrics : 0;
  }

  /**
   * 判断是否包含个人信息
   */
  containsPersonalInfo(message) {
    const infoPatterns = [
      /\d{1,3}[wkW万千]/, // 薪资
      /[A-Z]{2,}/, // 公司缩写
      /\d+年/, // 工作年限
      /(算法|开发|产品|运营|设计)/, // 技术栈
      /(北京|上海|深圳|杭州|广州)/, // 城市
    ];

    return infoPatterns.some((pattern) => pattern.test(message));
  }

  /**
   * 判断是否为关键信息收集时刻
   */
  isKeyInformationMoment(messageHistory) {
    if (messageHistory.length < 2) return false;

    const lastAssistantMessage = messageHistory
      .slice()
      .reverse()
      .find((msg) => msg.role === "assistant");

    if (!lastAssistantMessage) return false;

    // 检查是否刚询问了关键信息
    const keyQuestions = [
      "技术栈",
      "公司",
      "职级",
      "薪资",
      "期望",
      "城市",
      "方向",
    ];

    return keyQuestions.some((keyword) =>
      lastAssistantMessage.content.includes(keyword)
    );
  }

  /**
   * 更新会话中的候选人类型信息
   */
  async updateSessionCandidateType(sessionId, candidateType) {
    try {
      if (!candidateType || !this.database) return;

      const contextUpdate = {
        candidateType: candidateType,
        lastTypeAnalysis: Date.now(),
        typeHistory: [], // 可以记录类型变化历史
      };

      // 获取当前上下文
      const { data: session, error } = await this.database.client
        .from("chat_sessions")
        .select("current_interaction_context")
        .eq("id", sessionId)
        .single();

      if (!error && session) {
        const currentContext = session.current_interaction_context || {};

        // 记录类型变化历史
        if (
          currentContext.candidateType &&
          currentContext.candidateType.type !== candidateType.type
        ) {
          contextUpdate.typeHistory = currentContext.typeHistory || [];
          contextUpdate.typeHistory.push({
            previousType: currentContext.candidateType.type,
            newType: candidateType.type,
            changeTime: Date.now(),
          });
        }

        // 更新上下文
        const updatedContext = {
          ...currentContext,
          ...contextUpdate,
        };

        await this.database.client
          .from("chat_sessions")
          .update({ current_interaction_context: updatedContext })
          .eq("id", sessionId);

        console.log(
          `✅ 候选人类型已更新: ${candidateType.type} (置信度: ${candidateType.confidence})`
        );
      }
    } catch (error) {
      console.error("❌ 更新候选人类型失败:", error);
    }
  }

  /**
   * 获取候选人类型策略配置
   */
  getCandidateTypeStrategy(candidateType) {
    const strategies = {
      挤牙膏型: {
        maxRounds: 13,
        patience: "high",
        approach: "gentle_persistence",
        escalationPattern: [
          "耐心引导",
          "重复询问",
          "换个方式问",
          "最后尝试",
          "礼貌放弃",
        ],
        responseStyle: "耐心、重复、逐步引导",
        abandonThreshold: "no_response_after_multiple_prompts",
      },
      疯狂咨询型: {
        maxRounds: 9,
        patience: "medium",
        approach: "firm_boundaries",
        escalationPattern: [
          "坚持原则",
          "设定边界",
          "明确要求",
          "警告终止",
          "直接终止",
        ],
        responseStyle: "坚定、原则性、不妥协",
        abandonThreshold: "profanity_or_persistent_non_cooperation",
      },
      "质疑/不信任型": {
        maxRounds: 11,
        patience: "high",
        approach: "trust_building",
        escalationPattern: [
          "提供证据",
          "建立信任",
          "详细解释",
          "提供保证",
          "最终尝试",
        ],
        responseStyle: "专业、详细、可信",
        abandonThreshold: "persistent_distrust_after_evidence",
      },
      正常型: {
        maxRounds: 8,
        patience: "normal",
        approach: "standard_process",
        escalationPattern: ["标准流程", "正常推进", "适度催促", "完成转化"],
        responseStyle: "友好、专业、高效",
        abandonThreshold: "normal_completion_or_clear_rejection",
      },
    };

    return strategies[candidateType] || strategies["正常型"];
  }
}

module.exports = UserManager;
