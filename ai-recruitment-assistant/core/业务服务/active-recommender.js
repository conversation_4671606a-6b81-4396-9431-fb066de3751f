/**
 * AI招聘助手系统 - 主动推荐引擎
 *
 * 核心职责：
 * - 响应用户主动需求
 * - 特定条件推荐
 * - 第二次推荐处理
 * - 推荐策略调整
 *
 * 预计代码量：1500行
 */

class ActiveRecommender {
  constructor(database, config) {
    this.database = database;
    this.config = config;
    this.isInitialized = false;
  }

  /**
   * 初始化主动推荐引擎
   */
  async initialize() {
    try {
      this.isInitialized = true;
      console.log("🚀 主动推荐引擎初始化完成");
    } catch (error) {
      console.error("❌ 主动推荐引擎初始化失败:", error);
      throw error;
    }
  }

  /**
   * 根据用户需求生成推荐
   */
  async generateRecommendationsByRequest(userId, request) {
    try {
      // 解析用户需求
      const parsedRequest = this.parseUserRequest(request);

      // 生成推荐
      const recommendations = await this.findMatchingJobs(
        userId,
        parsedRequest
      );

      return {
        success: true,
        recommendations: recommendations,
        request: parsedRequest,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error("❌ 生成主动推荐失败:", error);
      throw error;
    }
  }

  /**
   * 解析用户请求
   */
  parseUserRequest(request) {
    // 解析用户的具体需求
    return {
      techDirection: null,
      salaryRange: null,
      location: null,
      companyType: null,
      experience: null,
    };
  }

  /**
   * 查找匹配的职位
   */
  async findMatchingJobs(userId, request) {
    try {
      // 实现职位匹配逻辑
      return [];
    } catch (error) {
      console.error("❌ 查找匹配职位失败:", error);
      return [];
    }
  }

  /**
   * 检查引擎状态
   */
  isReady() {
    return this.isInitialized;
  }

  // ==================== 职位推荐核心逻辑 ====================

  /**
   * 触发职位推荐
   */
  async triggerJobRecommendation(userId, infoState, session, userManager) {
    try {
      // 检查是否是第二次推荐（精准推荐）
      const isPreciseRecommendation =
        userManager.checkPreciseRecommendationEligibility(infoState);

      if (isPreciseRecommendation) {
        console.log("🎯 触发第二次精准推荐");
        return await this.generatePreciseRecommendation(userId, infoState);
      } else {
        console.log("🎯 触发第一次基础推荐");
        return await this.generateBasicRecommendation(
          userId,
          infoState,
          session
        );
      }
    } catch (error) {
      console.error("❌ 触发职位推荐失败:", error);
      return this.getErrorFallbackResponse();
    }
  }

  /**
   * 生成基础推荐（第一次推荐）
   */
  async generateBasicRecommendation(userId, infoState, session) {
    try {
      console.log("🔍 开始查询真实职位数据...");

      // 1. 映射技术方向到数据库ID
      const techDirectionId = await this.mapTechDirectionToId(
        infoState.技术方向
      );
      if (!techDirectionId) {
        console.error("❌ 无法映射技术方向:", infoState.技术方向);
        return this.getErrorFallbackResponse();
      }

      // 2. 查询匹配的职位（按4种公司类型分类）
      const jobsByCompanyType = await this.queryJobsByCompanyType(
        techDirectionId,
        infoState
      );

      // 3. 将职位数据扁平化为数组，便于后续查询详情
      const flatJobsList = [];
      Object.entries(jobsByCompanyType).forEach(([companyType, jobs]) => {
        jobs.forEach((job) => {
          flatJobsList.push(job);
        });
      });

      // 4. 生成推荐内容（使用格式化服务）
      const PassiveRecommender = require("./passive-recommender");

      const passiveRecommender = new PassiveRecommender(
        this.database,
        this.config
      );

      // 直接生成推荐内容，不使用AI服务（避免初始化延迟）
      const recommendationContent =
        await passiveRecommender.formatJobRecommendations(
          jobsByCompanyType,
          null
        );

      if (!recommendationContent) {
        return {
          type: "no_jobs_found",
          content:
            "抱歉，暂时没有找到完全匹配的职位，我会继续为您关注合适的机会。",
          metadata: {
            recommendationType: "basic",
            userInfo: infoState,
          },
        };
      }

      // 5. 将推荐的职位存储到会话上下文中，供后续查询详情使用
      await this.storeRecommendedJobs(session.session_uuid, flatJobsList);

      console.log("✅ 职位推荐详情已生成");

      return {
        type: "basic_recommendation",
        content: recommendationContent,
        metadata: {
          recommendationType: "basic",
          userInfo: infoState,
          jobsCount: flatJobsList.length,
        },
      };
    } catch (error) {
      console.error("❌ 生成基础推荐失败:", error);
      return this.getErrorFallbackResponse();
    }
  }

  /**
   * 生成精准推荐（第二次推荐）
   */
  async generatePreciseRecommendation(userId, infoState) {
    // TODO: 实现精准推荐逻辑 - 基于城市和业务场景的精准匹配
    return {
      type: "precise_recommendation",
      content: "好的，稍等，我再查询一下职位库",
      metadata: {
        recommendationType: "precise",
        userInfo: infoState,
        hasFollowUpMessage: true,
      },
    };
  }

  /**
   * 错误回退响应
   */
  getErrorFallbackResponse() {
    return {
      type: "error",
      content: "抱歉，系统遇到了一些技术问题，请稍后再试。",
      metadata: {
        error: true,
        timestamp: new Date().toISOString(),
      },
    };
  }

  // ==================== 数据查询方法 ====================

  /**
   * 映射技术方向到数据库ID
   */
  async mapTechDirectionToId(techDirection) {
    try {
      if (!techDirection) return null;

      const { data: techDirections, error } = await this.database.client
        .from("tech_tree")
        .select("id, tech_name")
        .ilike("tech_name", `%${techDirection}%`)
        .limit(1);

      if (error) {
        console.error("❌ 查询技术方向失败:", error);
        return null;
      }

      if (techDirections && techDirections.length > 0) {
        console.log(
          `✅ 技术方向映射: ${techDirection} → ID:${techDirections[0].id}`
        );
        return techDirections[0].id;
      }

      console.log(`⚠️ 未找到技术方向: ${techDirection}`);
      return null;
    } catch (error) {
      console.error("❌ 映射技术方向失败:", error);
      return null;
    }
  }

  /**
   * 按公司类型查询职位（4*4逻辑的核心）
   */
  async queryJobsByCompanyType(techDirectionId, infoState) {
    try {
      const companyTypes = ["头部大厂", "中型公司", "国企", "创业型公司"];
      const jobsByType = {};

      for (const companyType of companyTypes) {
        const jobs = await this.queryJobsByType(techDirectionId, companyType);
        if (jobs.length > 0) {
          jobsByType[companyType] = jobs;
          console.log(`✅ 找到${companyType}职位: ${jobs.length}个`);
        }
      }

      return jobsByType;
    } catch (error) {
      console.error("❌ 按公司类型查询职位失败:", error);
      return {};
    }
  }

  /**
   * 查询特定类型的职位
   */
  async queryJobsByType(techDirectionId, companyType) {
    try {
      const { data: jobs, error } = await this.database.client
        .from("job_listings")
        .select(
          `
          *,
          companies!inner(
            company_name,
            company_type
          )
        `
        )
        .eq("primary_tech_direction_id", techDirectionId)
        .eq("is_active", true)
        .eq("companies.company_type", companyType)
        .limit(1);

      if (error) {
        console.error(`❌ 查询${companyType}职位失败:`, error);
        return [];
      }

      return jobs || [];
    } catch (error) {
      console.error(`❌ 查询${companyType}职位异常:`, error);
      return [];
    }
  }

  /**
   * 存储推荐的职位到会话上下文
   */
  async storeRecommendedJobs(sessionUuid, jobsList) {
    try {
      // 获取会话ID
      const { data: session, error: sessionError } = await this.database.client
        .from("chat_sessions")
        .select("id")
        .eq("session_uuid", sessionUuid)
        .single();

      if (sessionError || !session) {
        console.error("❌ 获取会话失败:", sessionError);
        return false;
      }

      // 构建上下文更新
      const contextUpdate = {
        recommended_jobs: jobsList.map((job, index) => ({
          index: index + 1,
          job_id: job.id,
          company_name: job.companies?.company_name,
          job_title: job.job_title,
          stored_at: new Date().toISOString(),
        })),
        last_recommendation_time: new Date().toISOString(),
      };

      const { error: updateError } = await this.database.client
        .from("chat_sessions")
        .update({
          current_interaction_context: contextUpdate,
        })
        .eq("id", session.id);

      if (updateError) {
        console.error("❌ 更新会话上下文失败:", updateError);
        return false;
      }

      console.log(`✅ 已存储${jobsList.length}个推荐职位到会话上下文`);
      return true;
    } catch (error) {
      console.error("❌ 存储推荐职位失败:", error);
      return false;
    }
  }

  // ==================== 被动推荐检测 ====================

  /**
   * 检测用户是否询问特定公司类型
   */
  detectCompanyTypeInquiry(message) {
    try {
      const companyTypePatterns = {
        A: {
          keywords: [
            "大厂",
            "头部",
            "巨头",
            "big tech",
            "bat",
            "字节",
            "阿里",
            "腾讯",
            "百度",
            "美团",
          ],
          name: "头部大厂",
        },
        B: {
          keywords: [
            "国企",
            "国有",
            "央企",
            "state-owned",
            "中石油",
            "中石化",
            "国家电网",
            "中兴",
            "华为",
          ],
          name: "国企",
        },
        C: {
          keywords: ["中型", "中等", "中小", "独角兽", "上市", "知名"],
          name: "中型公司",
        },
        D: {
          keywords: ["创业", "startup", "初创", "小公司", "新兴"],
          name: "创业型公司",
        },
        外企: {
          keywords: [
            "外企",
            "外资",
            "foreign",
            "international",
            "跨国",
            "微软",
            "谷歌",
            "亚马逊",
            "苹果",
          ],
          name: "外企",
        },
      };

      const lowerMessage = message.toLowerCase();
      const detectedTypes = [];

      for (const [typeKey, typeInfo] of Object.entries(companyTypePatterns)) {
        for (const keyword of typeInfo.keywords) {
          if (lowerMessage.includes(keyword.toLowerCase())) {
            detectedTypes.push({
              type: typeKey,
              name: typeInfo.name,
              keyword: keyword,
              confidence: this.calculateKeywordConfidence(keyword, message),
            });
            break; // 找到一个关键词就跳出
          }
        }
      }

      // 按置信度排序
      detectedTypes.sort((a, b) => b.confidence - a.confidence);

      return {
        detected: detectedTypes.length > 0,
        types: detectedTypes,
        primaryType: detectedTypes[0] || null,
      };
    } catch (error) {
      console.error("❌ 公司类型检测失败:", error);
      return { detected: false, types: [], primaryType: null };
    }
  }

  /**
   * 计算关键词置信度
   */
  calculateKeywordConfidence(keyword, message) {
    const lowerMessage = message.toLowerCase();
    const lowerKeyword = keyword.toLowerCase();

    // 基础分数
    let confidence = 0.7;

    // 完整词匹配加分
    if (
      lowerMessage.includes(` ${lowerKeyword} `) ||
      lowerMessage.startsWith(`${lowerKeyword} `) ||
      lowerMessage.endsWith(` ${lowerKeyword}`)
    ) {
      confidence += 0.2;
    }

    // 问句形式加分
    if (
      lowerMessage.includes("有没有") ||
      lowerMessage.includes("推荐") ||
      lowerMessage.includes("?") ||
      lowerMessage.includes("？")
    ) {
      confidence += 0.1;
    }

    return Math.min(confidence, 1.0);
  }

  /**
   * 检测推荐意图强度
   */
  detectRecommendationIntent(message) {
    try {
      const intentPatterns = {
        strong: [
          "推荐",
          "介绍",
          "有没有",
          "找找",
          "看看",
          "帮我",
          "给我",
          "recommend",
          "suggest",
          "show me",
          "find",
        ],
        medium: [
          "了解",
          "知道",
          "听说",
          "考虑",
          "想要",
          "希望",
          "know",
          "understand",
          "consider",
          "want",
        ],
        weak: [
          "怎么样",
          "如何",
          "好吗",
          "行吗",
          "可以",
          "how",
          "what",
          "can",
          "could",
        ],
      };

      const lowerMessage = message.toLowerCase();
      let maxIntensity = 0;
      let detectedIntent = "none";

      for (const [intensity, patterns] of Object.entries(intentPatterns)) {
        for (const pattern of patterns) {
          if (lowerMessage.includes(pattern.toLowerCase())) {
            const intensityScore =
              intensity === "strong" ? 0.9 : intensity === "medium" ? 0.6 : 0.3;

            if (intensityScore > maxIntensity) {
              maxIntensity = intensityScore;
              detectedIntent = intensity;
            }
          }
        }
      }

      return {
        intent: detectedIntent,
        intensity: maxIntensity,
        hasIntent: maxIntensity > 0,
      };
    } catch (error) {
      console.error("❌ 推荐意图检测失败:", error);
      return { intent: "none", intensity: 0, hasIntent: false };
    }
  }

  /**
   * 综合分析被动推荐请求
   */
  analyzePassiveRecommendationRequest(message) {
    try {
      const companyTypeDetection = this.detectCompanyTypeInquiry(message);
      const intentDetection = this.detectRecommendationIntent(message);

      // 计算总体置信度
      let overallConfidence = 0;

      if (companyTypeDetection.detected && intentDetection.hasIntent) {
        overallConfidence =
          (companyTypeDetection.primaryType.confidence +
            intentDetection.intensity) /
          2;
      } else if (companyTypeDetection.detected) {
        overallConfidence = companyTypeDetection.primaryType.confidence * 0.7;
      } else if (intentDetection.hasIntent) {
        overallConfidence = intentDetection.intensity * 0.5;
      }

      return {
        isPassiveRecommendation: overallConfidence > 0.5,
        confidence: overallConfidence,
        companyTypes: companyTypeDetection.types,
        primaryCompanyType: companyTypeDetection.primaryType,
        intent: intentDetection,
        recommendation: this.generateRecommendationStrategy(
          companyTypeDetection,
          intentDetection,
          overallConfidence
        ),
      };
    } catch (error) {
      console.error("❌ 被动推荐请求分析失败:", error);
      return {
        isPassiveRecommendation: false,
        confidence: 0,
        companyTypes: [],
        primaryCompanyType: null,
        intent: { intent: "none", intensity: 0, hasIntent: false },
        recommendation: null,
      };
    }
  }

  /**
   * 生成推荐策略
   */
  generateRecommendationStrategy(
    companyTypeDetection,
    intentDetection,
    confidence
  ) {
    if (confidence < 0.5) {
      return null;
    }

    const strategy = {
      type: "passive_recommendation",
      confidence: confidence,
      action: "trigger_4x4_recommendation",
    };

    if (companyTypeDetection.detected) {
      strategy.targetCompanyType = companyTypeDetection.primaryType.type;
      strategy.companyTypeName = companyTypeDetection.primaryType.name;

      if (companyTypeDetection.primaryType.type === "外企") {
        // 外企需要特殊处理，因为数据库中可能没有外企分类
        strategy.action = "search_foreign_companies";
      }
    }

    if (intentDetection.intensity > 0.8) {
      strategy.urgency = "high";
    } else if (intentDetection.intensity > 0.5) {
      strategy.urgency = "medium";
    } else {
      strategy.urgency = "low";
    }

    return strategy;
  }
}

module.exports = ActiveRecommender;
