/**
 * AI招聘助手系统 - 数据库管理器
 *
 * 核心职责：
 * - Supabase 数据库连接管理
 * - 所有数据 CRUD 操作
 * - 查询优化和缓存
 * - 数据一致性保证
 *
 * 预计代码量：1800行
 */

const { createClient } = require("@supabase/supabase-js");

class DatabaseManager {
  constructor(config) {
    this.config = config;
    this.client = null;
    this.isConnected = false;
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5分钟缓存
  }

  /**
   * 建立数据库连接
   */
  async connect() {
    try {
      this.client = createClient(
        this.config.supabaseUrl,
        this.config.supabaseKey,
        {
          auth: {
            autoRefreshToken: false,
            persistSession: false,
          },
          db: {
            schema: "public",
          },
          global: {
            headers: {
              "x-client-info": "ai-recruitment-assistant",
            },
          },
        }
      );

      this.isConnected = true;
      console.log("🗄️ Supabase数据库连接成功");
    } catch (error) {
      console.error("❌ 数据库连接失败:", error);
      throw error;
    }
  }

  /**
   * 断开数据库连接
   */
  async disconnect() {
    if (this.client) {
      this.isConnected = false;
      this.cache.clear();
      console.log("🗄️ 数据库连接已断开");
    }
  }

  /**
   * 健康检查
   */
  async checkHealth() {
    try {
      const { data, error } = await this.client
        .from("users")
        .select("count")
        .limit(1);

      if (error) throw error;

      console.log("✅ 数据库健康检查通过");
      return true;
    } catch (error) {
      console.error("❌ 数据库健康检查失败:", error);
      throw error;
    }
  }

  // ==================== 用户数据操作 ====================

  /**
   * 创建用户
   */
  async createUser(userData) {
    try {
      const { data, error } = await this.client
        .from("users")
        .insert([
          {
            email: userData.email,
            user_type: userData.userType || "candidate",
            is_active: true,
            created_at: new Date().toISOString(),
          },
        ])
        .select()
        .single();

      if (error) throw error;

      console.log("✅ 用户创建成功:", data.id);
      return data;
    } catch (error) {
      console.error("❌ 用户创建失败:", error);
      throw error;
    }
  }

  /**
   * 根据邮箱获取用户
   */
  async getUserByEmail(email) {
    try {
      const cacheKey = `user_email_${email}`;
      const cached = this.getFromCache(cacheKey);
      if (cached) return cached;

      const { data, error } = await this.client
        .from("users")
        .select("*")
        .eq("email", email)
        .single();

      if (error && error.code !== "PGRST116") throw error;

      if (data) {
        this.setCache(cacheKey, data);
      }

      return data;
    } catch (error) {
      console.error("❌ 获取用户失败:", error);
      throw error;
    }
  }

  /**
   * 更新用户最后登录时间
   */
  async updateUserLastLogin(userId) {
    try {
      const { data, error } = await this.client
        .from("users")
        .update({ last_login_at: new Date().toISOString() })
        .eq("id", userId)
        .select()
        .single();

      if (error) throw error;

      // 清除相关缓存
      this.clearUserCache(userId);

      return data;
    } catch (error) {
      console.error("❌ 更新用户登录时间失败:", error);
      throw error;
    }
  }

  // ==================== 会话数据操作 ====================

  /**
   * 创建聊天会话
   */
  async createChatSession(sessionData) {
    try {
      const { data, error } = await this.client
        .from("chat_sessions")
        .insert([
          {
            user_id: sessionData.userId,
            session_uuid: sessionData.sessionUuid,
            entry_source_url: sessionData.entrySourceUrl || "",
            initial_intent: sessionData.initialIntent || "",
            current_interaction_context: sessionData.context || {},
            created_at: new Date().toISOString(),
            last_active_at: new Date().toISOString(),
          },
        ])
        .select()
        .single();

      if (error) throw error;

      console.log("✅ 会话创建成功:", data.id);
      return data;
    } catch (error) {
      console.error("❌ 会话创建失败:", error);
      throw error;
    }
  }

  /**
   * 根据UUID获取会话
   */
  async getSessionByUuid(sessionUuid) {
    try {
      // 检查是否是有效的UUID格式
      const uuidRegex =
        /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(sessionUuid)) {
        // 不是标准UUID格式，直接返回null而不是抛出错误
        console.log(`⚠️ 非标准UUID格式的sessionId: ${sessionUuid}`);
        return null;
      }

      const cacheKey = `session_${sessionUuid}`;
      const cached = this.getFromCache(cacheKey);
      if (cached) return cached;

      const { data, error } = await this.client
        .from("chat_sessions")
        .select("*")
        .eq("session_uuid", sessionUuid)
        .single();

      if (error && error.code !== "PGRST116") throw error;

      if (data) {
        this.setCache(cacheKey, data);
      }

      return data;
    } catch (error) {
      console.error("❌ 获取会话失败:", error);
      // 对于UUID格式错误，返回null而不是抛出错误
      if (error.code === "22P02") {
        console.log("⚠️ UUID格式错误，返回null");
        return null;
      }
      throw error;
    }
  }

  /**
   * 更新会话活跃时间
   */
  async updateSessionActivity(sessionId, context = null) {
    try {
      const updateData = {
        last_active_at: new Date().toISOString(),
      };

      if (context) {
        updateData.current_interaction_context = context;
      }

      const { data, error } = await this.client
        .from("chat_sessions")
        .update(updateData)
        .eq("id", sessionId)
        .select()
        .single();

      if (error) throw error;

      // 清除相关缓存
      this.clearSessionCache(sessionId);

      return data;
    } catch (error) {
      console.error("❌ 更新会话活跃时间失败:", error);
      throw error;
    }
  }

  // ==================== 消息数据操作 ====================

  /**
   * 保存聊天消息
   */
  async saveChatMessage(messageData) {
    try {
      // 使用传入的时间戳，如果没有则使用当前时间
      const timestamp =
        messageData.metadata?.timestamp || new Date().toISOString();

      const { data, error } = await this.client
        .from("chat_messages")
        .insert([
          {
            session_id: messageData.sessionId,
            message_type: messageData.messageType,
            message_content: messageData.content,
            metadata_json: messageData.metadata || {},
            timestamp: timestamp,
          },
        ])
        .select()
        .single();

      if (error) throw error;

      return data;
    } catch (error) {
      console.error("❌ 保存消息失败:", error);
      throw error;
    }
  }

  /**
   * 获取会话消息历史
   */
  async getSessionMessages(sessionId, limit = 50) {
    try {
      const { data, error } = await this.client
        .from("chat_messages")
        .select("*")
        .eq("session_id", sessionId)
        .order("id", { ascending: true }) // 使用自增ID排序，确保插入顺序
        .limit(limit);

      if (error) throw error;

      return data; // 直接返回，不需要reverse
    } catch (error) {
      console.error("❌ 获取消息历史失败:", error);
      throw error;
    }
  }

  // ==================== 候选人档案操作 ====================

  /**
   * 创建或更新候选人档案
   */
  async upsertCandidateProfile(userId, profileData) {
    try {
      const { data, error } = await this.client
        .from("candidate_profiles")
        .upsert(
          {
            user_id: userId,
            ...profileData,
            last_updated_at: new Date().toISOString(),
          },
          {
            onConflict: "user_id",
            ignoreDuplicates: false,
          }
        )
        .select()
        .single();

      if (error) throw error;

      // 清除相关缓存
      this.clearProfileCache(userId);

      console.log("✅ 候选人档案更新成功");
      return data;
    } catch (error) {
      console.error("❌ 候选人档案更新失败:", error);
      throw error;
    }
  }

  /**
   * 获取候选人档案
   */
  async getCandidateProfile(userId) {
    try {
      const cacheKey = `profile_${userId}`;
      const cached = this.getFromCache(cacheKey);
      if (cached) return cached;

      const { data, error } = await this.client
        .from("candidate_profiles")
        .select("*")
        .eq("user_id", userId)
        .single();

      if (error && error.code !== "PGRST116") throw error;

      if (data) {
        this.setCache(cacheKey, data);
      }

      return data;
    } catch (error) {
      console.error("❌ 获取候选人档案失败:", error);
      throw error;
    }
  }

  // ==================== 职位数据操作 ====================

  /**
   * 搜索职位
   */
  async searchJobs(criteria = {}) {
    try {
      let query = this.client
        .from("job_listings")
        .select(
          `
          *,
          companies (
            id,
            company_name,
            company_type,
            industry,
            logo_url
          )
        `
        )
        .eq("is_active", true);

      // 技术方向筛选
      if (criteria.techDirectionId) {
        query = query.eq("primary_tech_direction_id", criteria.techDirectionId);
      }

      // 业务场景筛选
      if (criteria.businessScenarioId) {
        query = query.eq(
          "primary_business_scenario_id",
          criteria.businessScenarioId
        );
      }

      // 薪资范围筛选
      if (criteria.salaryMin) {
        query = query.gte("salary_min", criteria.salaryMin);
      }
      if (criteria.salaryMax) {
        query = query.lte("salary_max", criteria.salaryMax);
      }

      // 经验要求筛选
      if (criteria.experienceMin) {
        query = query.gte("job_standard_level_min", criteria.experienceMin);
      }
      if (criteria.experienceMax) {
        query = query.lte("job_standard_level_max", criteria.experienceMax);
      }

      // 排序和限制
      query = query
        .order("priority_level", { ascending: false })
        .order("created_at", { ascending: false })
        .limit(criteria.limit || 20);

      const { data, error } = await query;

      if (error) throw error;

      return data || [];
    } catch (error) {
      console.error("❌ 搜索职位失败:", error);
      throw error;
    }
  }

  /**
   * 获取职位详情
   */
  async getJobDetails(jobId) {
    try {
      const { data, error } = await this.client
        .from("job_listings")
        .select(
          `
          *,
          companies (
            id,
            company_name,
            company_type,
            industry,
            description,
            website,
            logo_url
          )
        `
        )
        .eq("id", jobId)
        .single();

      if (error) throw error;

      return data;
    } catch (error) {
      console.error("❌ 获取职位详情失败:", error);
      throw error;
    }
  }

  // ==================== 技术树数据操作 ====================

  /**
   * 获取技术树数据
   */
  async getTechTree() {
    try {
      const cacheKey = "tech_tree_all";
      const cached = this.getFromCache(cacheKey);
      if (cached) return cached;

      const { data, error } = await this.client
        .from("tech_tree")
        .select("*")
        .order("level", { ascending: true })
        .order("tech_name", { ascending: true });

      if (error) throw error;

      this.setCache(cacheKey, data);
      return data || [];
    } catch (error) {
      console.error("❌ 获取技术树失败:", error);
      throw error;
    }
  }

  /**
   * 根据关键词搜索技术
   */
  async searchTechByKeyword(keyword) {
    try {
      const { data, error } = await this.client
        .from("tech_tree")
        .select("*")
        .or(`tech_name.ilike.%${keyword}%,keywords.ilike.%${keyword}%`)
        .order("level", { ascending: true })
        .limit(20);

      if (error) throw error;

      return data || [];
    } catch (error) {
      console.error("❌ 搜索技术失败:", error);
      throw error;
    }
  }

  // ==================== 业务场景数据操作 ====================

  /**
   * 获取业务场景数据
   */
  async getBusinessScenarios() {
    try {
      const cacheKey = "business_scenarios_all";
      const cached = this.getFromCache(cacheKey);
      if (cached) return cached;

      const { data, error } = await this.client
        .from("business_scenarios")
        .select("*")
        .order("level", { ascending: true })
        .order("scenario_name", { ascending: true });

      if (error) throw error;

      this.setCache(cacheKey, data);
      return data || [];
    } catch (error) {
      console.error("❌ 获取业务场景失败:", error);
      throw error;
    }
  }

  // ==================== 缓存管理 ====================

  /**
   * 设置缓存
   */
  setCache(key, value) {
    this.cache.set(key, {
      data: value,
      timestamp: Date.now(),
    });
  }

  /**
   * 获取缓存
   */
  getFromCache(key) {
    const cached = this.cache.get(key);
    if (!cached) return null;

    if (Date.now() - cached.timestamp > this.cacheTimeout) {
      this.cache.delete(key);
      return null;
    }

    return cached.data;
  }

  /**
   * 清除用户相关缓存
   */
  clearUserCache(userId) {
    const keysToDelete = [];
    for (const key of this.cache.keys()) {
      if (key.includes(`user_${userId}`) || key.includes(`profile_${userId}`)) {
        keysToDelete.push(key);
      }
    }
    keysToDelete.forEach((key) => this.cache.delete(key));
  }

  /**
   * 清除会话相关缓存
   */
  clearSessionCache(sessionId) {
    const keysToDelete = [];
    for (const key of this.cache.keys()) {
      if (key.includes(`session_${sessionId}`)) {
        keysToDelete.push(key);
      }
    }
    keysToDelete.forEach((key) => this.cache.delete(key));
  }

  /**
   * 清除档案相关缓存
   */
  clearProfileCache(userId) {
    const keysToDelete = [];
    for (const key of this.cache.keys()) {
      if (key.includes(`profile_${userId}`)) {
        keysToDelete.push(key);
      }
    }
    keysToDelete.forEach((key) => this.cache.delete(key));
  }

  /**
   * 清除所有缓存
   */
  clearAllCache() {
    this.cache.clear();
    console.log("🗑️ 所有缓存已清除");
  }

  // ==================== 记忆管理系统 ====================

  /**
   * 保存用户长期记忆
   */
  async saveUserMemory(
    userId,
    category,
    keyName,
    valueContent,
    confidenceScore = 0.5,
    sourceSessionId = null
  ) {
    try {
      // 先检查是否已存在
      const { data: existing } = await this.client
        .from("user_memory")
        .select("id")
        .eq("user_id", userId)
        .eq("memory_category", category)
        .eq("key_name", keyName)
        .single();

      let data, error;

      if (existing) {
        // 更新现有记录
        const result = await this.client
          .from("user_memory")
          .update({
            value_content: valueContent,
            confidence_score: confidenceScore,
            source_session_id: sourceSessionId,
            updated_at: new Date().toISOString(),
          })
          .eq("id", existing.id)
          .select()
          .single();
        data = result.data;
        error = result.error;
      } else {
        // 插入新记录
        const result = await this.client
          .from("user_memory")
          .insert({
            user_id: userId,
            memory_category: category,
            key_name: keyName,
            value_content: valueContent,
            confidence_score: confidenceScore,
            source_session_id: sourceSessionId,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          })
          .select()
          .single();
        data = result.data;
        error = result.error;
      }

      if (error) throw error;

      // 清除相关缓存
      this.clearUserMemoryCache(userId);

      console.log(`✅ 用户记忆保存成功: ${category}.${keyName}`);
      return data;
    } catch (error) {
      console.error("❌ 保存用户记忆失败:", error);
      throw error;
    }
  }

  /**
   * 获取用户记忆
   */
  async getUserMemory(userId, category = null, keyName = null) {
    try {
      const cacheKey = `user_memory_${userId}_${category || "all"}_${keyName || "all"}`;
      const cached = this.getFromCache(cacheKey);
      if (cached) return cached;

      let query = this.client
        .from("user_memory")
        .select("*")
        .eq("user_id", userId)
        .order("confidence_score", { ascending: false })
        .order("updated_at", { ascending: false });

      if (category) {
        query = query.eq("memory_category", category);
      }

      if (keyName) {
        query = query.eq("key_name", keyName);
      }

      const { data, error } = await query;

      if (error) throw error;

      if (data) {
        this.setCache(cacheKey, data);
      }

      return data || [];
    } catch (error) {
      console.error("❌ 获取用户记忆失败:", error);
      throw error;
    }
  }

  /**
   * 清除用户记忆相关缓存
   */
  clearUserMemoryCache(userId) {
    const keysToDelete = [];
    for (const key of this.cache.keys()) {
      if (key.includes(`user_memory_${userId}`)) {
        keysToDelete.push(key);
      }
    }
    keysToDelete.forEach((key) => this.cache.delete(key));
  }

  // ==================== 候选人类型长期缓存管理 ====================

  /**
   * 获取候选人类型缓存（14天统一缓存）
   */
  async getCandidateTypeCache(sessionId) {
    try {
      const fs = require("fs").promises;
      const path = require("path");

      const cacheDir = "./cache/candidate_types";
      const filePath = path.join(cacheDir, `${sessionId}.json`);

      const data = await fs.readFile(filePath, "utf8");
      const cacheData = JSON.parse(data);

      // 检查是否过期（从环境变量读取天数）
      const expiry =
        (parseInt(process.env.CACHE_EXPIRY_DAYS) || 14) * 24 * 60 * 60 * 1000;
      if (Date.now() - cacheData.timestamp > expiry) {
        await this.removeCandidateTypeCache(sessionId);
        return null;
      }

      return cacheData;
    } catch (error) {
      return null; // 文件不存在或读取失败
    }
  }

  /**
   * 保存候选人类型缓存
   */
  async setCandidateTypeCache(sessionId, analysis, rounds) {
    try {
      const fs = require("fs").promises;
      const path = require("path");

      const cacheDir = "./cache/candidate_types";
      await fs.mkdir(cacheDir, { recursive: true });

      const cacheData = {
        data: analysis,
        timestamp: Date.now(),
        rounds: rounds,
        sessionId: sessionId,
      };

      const filePath = path.join(cacheDir, `${sessionId}.json`);
      await fs.writeFile(filePath, JSON.stringify(cacheData, null, 2));
    } catch (error) {
      console.error("❌ 候选人类型缓存保存失败:", error);
    }
  }

  /**
   * 删除候选人类型缓存
   */
  async removeCandidateTypeCache(sessionId) {
    try {
      const fs = require("fs").promises;
      const path = require("path");

      const filePath = path.join(
        "./cache/candidate_types",
        `${sessionId}.json`
      );
      await fs.unlink(filePath);
    } catch (error) {
      if (error.code !== "ENOENT") {
        console.error("❌ 候选人类型缓存删除失败:", error);
      }
    }
  }

  /**
   * 清理过期的候选人类型缓存
   */
  async cleanupExpiredCandidateTypeCache() {
    try {
      const fs = require("fs").promises;
      const path = require("path");

      const cacheDir = "./cache/candidate_types";

      try {
        await fs.access(cacheDir);
      } catch {
        return; // 目录不存在
      }

      const files = await fs.readdir(cacheDir);
      const expiry =
        (parseInt(process.env.CACHE_EXPIRY_DAYS) || 14) * 24 * 60 * 60 * 1000;
      let cleanedCount = 0;

      for (const file of files) {
        if (!file.endsWith(".json")) continue;

        const filePath = path.join(cacheDir, file);

        try {
          const data = await fs.readFile(filePath, "utf8");
          const cacheData = JSON.parse(data);

          if (Date.now() - cacheData.timestamp > expiry) {
            await fs.unlink(filePath);
            cleanedCount++;
          }
        } catch {
          // 文件损坏，直接删除
          try {
            await fs.unlink(filePath);
            cleanedCount++;
          } catch {}
        }
      }

      if (cleanedCount > 0) {
        console.log(`🧹 清理了 ${cleanedCount} 个过期候选人类型缓存`);
      }
    } catch (error) {
      console.error("❌ 候选人类型缓存清理失败:", error);
    }
  }
}

module.exports = DatabaseManager;
