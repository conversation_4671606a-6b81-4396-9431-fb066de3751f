/**
 * AI招聘助手系统 - AI服务模块
 *
 * 核心职责：
 * - 双模型架构管理（Qwen-Turbo + DeepSeek V3）
 * - 候选人类型智能分析
 * - 个性化回复生成
 * - 上下文理解和信息提取
 *
 * 预计代码量：1800行
 */

const axios = require("axios");

class AIServices {
  constructor(config) {
    this.config = config;
    this.deepseekClient = null;
    this.qwenClient = null;
    this.isInitialized = false;

    // DeepSeek API配置
    this.deepseekEndpoint = config.deepseekEndpoint;
    this.deepseekApiKey = config.deepseekApiKey;

    // Qwen-Turbo API配置
    this.qwenEndpoint = config.qwenEndpoint;
    this.qwenApiKey = config.qwenApiKey;

    // 通用配置
    this.maxTokens = config.maxTokens;
    this.temperature = config.temperature;
    this.timeout = config.timeout;

    // 候选人类型缓存策略（从环境变量读取配置）
    this.candidateTypeCache = new Map(); // 内存缓存（临时）
    this.cacheStrategy = {
      // 统一缓存策略：从环境变量读取缓存天数
      uniformExpiry:
        (parseInt(process.env.CACHE_EXPIRY_DAYS) || 14) * 24 * 60 * 60 * 1000,

      // 重新分析触发条件（从环境变量读取）
      behaviorChangeThreshold:
        parseFloat(process.env.BEHAVIOR_CHANGE_THRESHOLD) || 0.4,
      confidenceThreshold: parseFloat(process.env.CONFIDENCE_THRESHOLD) || 0.6,
      roundsThreshold: parseInt(process.env.ROUNDS_THRESHOLD) || 10,

      // 文件缓存配置
      useFileCache: true,
      cacheDirectory: process.env.CACHE_DIRECTORY || "./cache/candidate_types",
      maxCacheSize: parseInt(process.env.MAX_CACHE_SIZE) || 50000,
      cleanupInterval: 24 * 60 * 60 * 1000, // 24小时清理一次
    };

    // 高并发优化配置
    this.requestQueue = [];
    this.processing = false;
    this.maxRetries = 3;
    this.rateLimitDelay = 1000;
    this.maxConcurrentRequests = 5;
    this.currentRequests = 0;

    // 请求统计
    this.stats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      rateLimitHits: 0,
      qwenCalls: 0,
      deepseekCalls: 0,
      cacheHits: 0,
    };

    // 模型健康状态监控
    this.modelHealth = {
      qwen: {
        isHealthy: true,
        lastCheck: Date.now(),
        consecutiveFailures: 0,
        maxFailures: 3,
        lastError: null,
      },
      deepseek: {
        isHealthy: true,
        lastCheck: Date.now(),
        consecutiveFailures: 0,
        maxFailures: 3,
        lastError: null,
      },
    };

    // 费用监控和预警
    this.costMonitor = {
      dailyLimit: config.dailyCostLimit || 100, // 默认100元/天
      currentDailyCost: 0,
      lastResetDate: new Date().toDateString(),
      alertThreshold: 0.8, // 80%时告警
      isBlocked: false,
    };

    // 容灾策略配置
    this.fallbackStrategies = {
      qwenFallback: "rule_based", // qwen故障时使用规则引擎
      deepseekFallback: "template_response", // deepseek故障时使用模板回复
      enableGracefulDegradation: true,
    };
  }

  /**
   * 初始化AI服务
   */
  async initialize() {
    try {
      // 配置DeepSeek客户端
      this.deepseekClient = axios.create({
        baseURL: this.deepseekEndpoint.replace("/chat/completions", ""),
        timeout: this.timeout,
        headers: {
          Authorization: `Bearer ${this.deepseekApiKey}`,
          "Content-Type": "application/json",
        },
      });

      // 配置Qwen客户端
      this.qwenClient = axios.create({
        baseURL: this.qwenEndpoint,
        timeout: this.timeout,
        headers: {
          Authorization: `Bearer ${this.qwenApiKey}`,
          "Content-Type": "application/json",
          "X-DashScope-SSE": "disable",
        },
      });

      // 测试连接
      await this.testConnection();

      this.isInitialized = true;
      console.log("🤖 AI服务初始化完成");
    } catch (error) {
      console.error("❌ AI服务初始化失败:", error);
      // 不抛出错误，允许降级运行
      this.isInitialized = false;
    }
  }

  /**
   * 测试AI服务连接
   */
  async testConnection() {
    try {
      // 测试DeepSeek连接
      if (
        this.deepseekApiKey &&
        this.deepseekApiKey !== "test-key" &&
        this.deepseekApiKey.startsWith("sk-")
      ) {
        const response = await this.deepseekClient.post("/chat/completions", {
          model: "deepseek-chat",
          messages: [{ role: "user", content: "Hello" }],
          max_tokens: 10,
        });

        if (response.status === 200) {
          console.log("✅ DeepSeek连接测试成功");
          this.modelHealth.deepseek.isHealthy = true;
        }
      } else {
        console.log("⚠️ DeepSeek API Key未配置，跳过连接测试");
      }

      // 测试Qwen连接
      if (
        this.qwenApiKey &&
        this.qwenApiKey !== "test-key" &&
        this.qwenApiKey.startsWith("sk-")
      ) {
        // Qwen使用不同的API格式，这里只标记为健康
        console.log("✅ Qwen API Key已配置");
        this.modelHealth.qwen.isHealthy = true;
      } else {
        console.log("⚠️ Qwen API Key未配置，跳过连接测试");
      }
    } catch (error) {
      console.error("❌ AI服务连接测试失败:", error);
      // 不抛出错误，允许降级运行
      this.modelHealth.deepseek.isHealthy = false;
      this.modelHealth.qwen.isHealthy = false;
    }
  }

  /**
   * 分析用户意图（带队列处理）
   */
  async analyzeUserIntent(message, context = {}) {
    return this.queueRequest(
      async () => {
        const prompt = this.buildIntentAnalysisPrompt(message, context);

        const response = await this.makeAPIRequest("/chat/completions", {
          model: "deepseek-chat",
          messages: [
            { role: "system", content: this.getIntentAnalysisSystemPrompt() },
            { role: "user", content: prompt },
          ],
          max_tokens: 500,
          temperature: 0.3,
        });

        const result = response.data.choices[0].message.content;
        return this.parseIntentAnalysisResult(result);
      },
      {
        fallback: {
          type: "unknown",
          confidence: 0.1,
          entities: {},
        },
      }
    );
  }

  /**
   * 请求队列处理
   */
  async queueRequest(requestFn, options = {}) {
    return new Promise((resolve, reject) => {
      this.requestQueue.push({
        requestFn,
        resolve,
        reject,
        options,
        retries: 0,
        timestamp: Date.now(),
      });

      this.processQueue();
    });
  }

  /**
   * 处理请求队列
   */
  async processQueue() {
    if (this.processing || this.requestQueue.length === 0) {
      return;
    }

    if (this.currentRequests >= this.maxConcurrentRequests) {
      return;
    }

    this.processing = true;
    const request = this.requestQueue.shift();
    this.currentRequests++;

    try {
      const startTime = Date.now();
      const result = await request.requestFn();

      // 更新统计信息
      this.updateStats(true, Date.now() - startTime);

      request.resolve(result);
    } catch (error) {
      console.error("❌ AI请求失败:", error);

      // 检查是否需要重试
      if (request.retries < this.maxRetries && this.shouldRetry(error)) {
        request.retries++;
        this.requestQueue.unshift(request); // 重新加入队列头部

        // 如果是限流错误，延迟处理
        if (this.isRateLimitError(error)) {
          this.stats.rateLimitHits++;
          await this.delay(this.rateLimitDelay);
        }
      } else {
        // 重试次数用完或不可重试错误，返回降级结果
        this.updateStats(false, 0);

        if (request.options.fallback) {
          request.resolve(request.options.fallback);
        } else {
          request.reject(error);
        }
      }
    } finally {
      this.currentRequests--;
      this.processing = false;

      // 继续处理队列
      setTimeout(() => this.processQueue(), 10);
    }
  }

  /**
   * 发起API请求（带重试和限流处理）
   */
  async makeAPIRequest(endpoint, data) {
    try {
      const response = await this.client.post(endpoint, data);
      return response;
    } catch (error) {
      // 处理特定错误类型
      if (error.response?.status === 429) {
        // 限流错误
        throw new Error("RATE_LIMIT");
      } else if (error.response?.status >= 500) {
        // 服务器错误，可重试
        throw new Error("SERVER_ERROR");
      } else {
        // 客户端错误，不重试
        throw error;
      }
    }
  }

  /**
   * 判断是否应该重试
   */
  shouldRetry(error) {
    const retryableErrors = ["RATE_LIMIT", "SERVER_ERROR", "TIMEOUT"];
    return (
      retryableErrors.includes(error.message) ||
      error.code === "ECONNRESET" ||
      error.code === "ETIMEDOUT"
    );
  }

  /**
   * 判断是否是限流错误
   */
  isRateLimitError(error) {
    return error.message === "RATE_LIMIT" || error.response?.status === 429;
  }

  /**
   * 延迟函数
   */
  delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * 更新统计信息
   */
  updateStats(success, responseTime) {
    this.stats.totalRequests++;

    if (success) {
      this.stats.successfulRequests++;

      // 计算平均响应时间
      const totalTime =
        this.stats.averageResponseTime * (this.stats.successfulRequests - 1) +
        responseTime;
      this.stats.averageResponseTime =
        totalTime / this.stats.successfulRequests;
    } else {
      this.stats.failedRequests++;
    }
  }

  /**
   * 构建意图分析提示词
   */
  buildIntentAnalysisPrompt(message, context) {
    let prompt = `用户消息: "${message}"\n\n`;

    if (context.sessionContext) {
      prompt += `会话上下文: ${JSON.stringify(context.sessionContext)}\n\n`;
    }

    if (context.messageHistory && context.messageHistory.length > 0) {
      prompt += `最近对话历史:\n`;
      context.messageHistory.forEach((msg, index) => {
        prompt += `${msg.message_type}: ${msg.message_content}\n`;
      });
      prompt += "\n";
    }

    prompt += `请分析用户的意图类型、置信度和相关实体信息。`;

    return prompt;
  }

  /**
   * 获取意图分析系统提示词
   */
  getIntentAnalysisSystemPrompt() {
    return `你是一个专业的招聘助手意图分析器。请分析用户消息的意图类型。

可能的意图类型包括：
- greeting: 问候语
- profile_update: 更新个人档案
- job_search: 搜索职位
- recommendation_request: 请求推荐
- tech_direction_inquiry: 技术方向询问
- salary_inquiry: 薪资询问
- company_inquiry: 公司询问
- location_inquiry: 地点询问
- experience_inquiry: 经验询问
- resume_upload: 简历上传
- unknown: 未知意图

请以JSON格式返回分析结果：
{
  "type": "意图类型",
  "confidence": 0.0-1.0的置信度,
  "entities": {
    "技术栈": ["提取的技术"],
    "经验年限": "提取的年限",
    "期望薪资": "提取的薪资",
    "工作地点": "提取的地点"
  },
  "context": {
    "需要澄清": true/false,
    "澄清问题": "需要澄清的具体问题"
  }
}`;
  }

  /**
   * 解析意图分析结果
   */
  parseIntentAnalysisResult(result) {
    try {
      // 清理结果，移除markdown代码块标记
      let cleanResult = result.trim();
      if (cleanResult.startsWith("```json")) {
        cleanResult = cleanResult
          .replace(/^```json\s*/, "")
          .replace(/\s*```$/, "");
      } else if (cleanResult.startsWith("```")) {
        cleanResult = cleanResult.replace(/^```\s*/, "").replace(/\s*```$/, "");
      }

      // 修复常见的JSON格式问题
      cleanResult = cleanResult
        .replace(/,(\s*[}\]])/g, "$1") // 移除对象和数组末尾的多余逗号
        .replace(/'/g, '"'); // 将单引号替换为双引号

      // 尝试解析JSON
      const parsed = JSON.parse(cleanResult);

      return {
        type: parsed.type || "unknown",
        confidence: parsed.confidence || 0.5,
        entities: parsed.entities || {},
        context: parsed.context || {},
      };
    } catch (error) {
      console.error("❌ 解析意图分析结果失败:", error);

      // 回退到简单解析
      return {
        type: "unknown",
        confidence: 0.3,
        entities: {},
      };
    }
  }

  /**
   * 生成对话回复（带队列处理）
   */
  async generateResponse(prompt, context = {}) {
    return this.queueRequest(
      async () => {
        const messages = [
          { role: "system", content: this.getConversationSystemPrompt() },
          { role: "user", content: prompt },
        ];

        // 添加上下文消息
        if (context.messageHistory) {
          const historyMessages = context.messageHistory
            .slice(-5)
            .map((msg) => ({
              role: msg.message_type === "user" ? "user" : "assistant",
              content: msg.message_content,
            }));

          messages.splice(1, 0, ...historyMessages);
        }

        const response = await this.makeAPIRequest("/chat/completions", {
          model: "deepseek-chat",
          messages: messages,
          max_tokens: this.maxTokens,
          temperature: this.temperature,
        });

        return response.data.choices[0].message.content;
      },
      {
        fallback: "抱歉，我现在无法生成回复。请稍后再试。",
      }
    );
  }

  /**
   * 获取对话系统提示词
   */
  getConversationSystemPrompt() {
    return `你是Katrina，一个专业的AI招聘助手。

【严格限制 - 绝对禁止的行为】：
1. 绝对不能虚构、编造或推荐具体的职位信息
2. 绝对不能提及具体的公司名称（除非用户主动提及）
3. 绝对不能给出具体的薪资数字或范围
4. 绝对不能编造招聘信息或职位详情

【你的唯一作用】：
1. 收集用户信息：技术栈、工作经验、期望薪资、工作地点等
2. 引导用户完善个人档案信息
3. 对用户提供的信息进行确认和澄清
4. 提供一般性的职业发展建议（不涉及具体职位）

【标准回复模式】：
- 当用户询问职位时，引导其提供个人信息
- 当信息不完整时，询问缺失的关键信息
- 当用户提供信息时，确认并询问是否还有补充

【回复原则】：
- 简洁、专业、友好
- 每次回复重点突出1-2个关键问题
- 避免长篇大论
- 不要主动提及具体的技术栈或公司

记住：你的作用是信息收集和引导，不是职位推荐！`;
  }

  /**
   * 检查服务状态
   */
  isReady() {
    return this.isInitialized;
  }

  // ==================== 候选人类型分析模块 ====================

  /**
   * 分析候选人类型（使用Qwen-Turbo，支持14天长期缓存）
   */
  async analyzeCandidateType(dialogueHistory, sessionId, forceRefresh = false) {
    try {
      // 检查缓存（内存 + 数据库）
      if (!forceRefresh) {
        const cachedResult = await this.getCachedCandidateType(
          sessionId,
          dialogueHistory.length
        );
        if (cachedResult) {
          this.stats.cacheHits++;
          return cachedResult;
        }
      }

      // 检查Qwen模型健康状态
      if (!this.modelHealth.qwen.isHealthy) {
        console.warn("⚠️ Qwen模型不健康，使用规则引擎分析");
        return this.fallbackCandidateTypeAnalysis(dialogueHistory);
      }

      // 检查费用限制
      if (this.costMonitor.isBlocked) {
        console.warn("⚠️ 达到费用限制，使用规则引擎分析");
        return this.fallbackCandidateTypeAnalysis(dialogueHistory);
      }

      // 构建分析prompt
      const analysisPrompt = this.buildCandidateAnalysisPrompt(dialogueHistory);

      // 调用Qwen-Turbo进行分析
      const result = await this.callQwenTurbo(analysisPrompt, {
        maxTokens: 200,
        temperature: 0.1,
      });

      const analysis = this.parseCandidateTypeResult(result);

      // 缓存结果（内存 + 数据库持久化）
      await this.setCachedCandidateType(
        sessionId,
        analysis,
        dialogueHistory.length
      );

      // 更新统计
      this.stats.qwenCalls++;
      this.updateCostTracking("qwen", analysisPrompt.length, result.length);

      return analysis;
    } catch (error) {
      console.error("❌ 候选人类型分析失败:", error);
      this.handleModelError("qwen", error);
      return this.fallbackCandidateTypeAnalysis(dialogueHistory);
    }
  }

  /**
   * 构建候选人类型分析prompt
   */
  buildCandidateAnalysisPrompt(dialogueHistory) {
    // 分析对话中的关键行为模式
    const behaviorAnalysis = this.analyzeBehaviorPatterns(dialogueHistory);

    const prompt = `请基于以下对话和行为分析，判断候选人类型（不依赖轮次，基于行为模式）：

对话历史：
${dialogueHistory.map((msg, index) => `${index + 1}. ${msg.role}: ${msg.content}`).join("\n")}

行为模式分析：
- 平均消息长度: ${behaviorAnalysis.avgMessageLength.toFixed(1)}字
- 问题比例: ${(behaviorAnalysis.questionRatio * 100).toFixed(1)}%
- 短回复比例: ${(behaviorAnalysis.shortReplyRatio * 100).toFixed(1)}%
- 信息提供比例: ${(behaviorAnalysis.infoProvisionRatio * 100).toFixed(1)}%
- 质疑表达: ${behaviorAnalysis.doubtExpressions.length}次
- 回避行为: ${behaviorAnalysis.avoidanceBehaviors.length}次

候选人类型特征（基于行为模式，不限轮次）：

1. 挤牙膏型：消息极短、信息提供少、被动回应
2. 疯狂咨询型：问题多但不提供信息、回避直答
3. 质疑/不信任型：质疑真实性、要求证明、谨慎配合
4. 正常型：正常配合、主动提供信息、问答平衡

请分析并返回JSON格式：
{
  "type": "候选人类型",
  "confidence": 0.8,
  "evidence": ["具体证据1", "具体证据2"],
  "strategy": "建议策略",
  "riskLevel": "low/medium/high",
  "behaviorPattern": {
    "primaryBehavior": "主要行为特征",
    "secondaryBehaviors": ["次要特征1", "次要特征2"]
  }
}`;

    return prompt;
  }

  /**
   * 分析对话中的行为模式（使用utilities工具）
   */
  analyzeBehaviorPatterns(dialogueHistory) {
    const Utilities = require("../工具库/utilities");
    const utils = new Utilities();

    const userMessages = dialogueHistory.filter((msg) => msg.role === "user");

    if (userMessages.length === 0) {
      return {
        avgMessageLength: 0,
        questionRatio: 0,
        shortReplyRatio: 0,
        infoProvisionRatio: 0,
        doubtExpressions: [],
        avoidanceBehaviors: [],
      };
    }

    // 使用utilities的基础分析
    const basicAnalysis = utils.analyzeBehaviorPattern(userMessages);

    // 识别质疑表达
    const doubtPatterns = [
      /真的吗|确定吗|不是在|听说|怀疑/,
      /靠谱吗|可信吗|有保证吗/,
      /不会是|该不会|不太相信/,
    ];
    const doubtExpressions = userMessages.filter((msg) =>
      doubtPatterns.some((pattern) => pattern.test(msg.content))
    );

    // 识别回避行为
    const avoidancePatterns = [
      /你先说|你有什么|先看看/,
      /不方便|不想说|再说吧/,
      /换个话题|算了|不聊这个/,
    ];
    const avoidanceBehaviors = userMessages.filter((msg) =>
      avoidancePatterns.some((pattern) => pattern.test(msg.content))
    );

    return {
      ...basicAnalysis,
      doubtExpressions,
      avoidanceBehaviors,
    };
  }

  // ==================== 长期缓存管理模块 ====================

  /**
   * 获取缓存的候选人类型（统一14天缓存）
   */
  async getCachedCandidateType(sessionId, currentRounds) {
    try {
      // 1. 先检查内存缓存
      if (this.candidateTypeCache.has(sessionId)) {
        const cached = this.candidateTypeCache.get(sessionId);

        if (Date.now() - cached.timestamp < this.cacheStrategy.uniformExpiry) {
          // 检查是否需要重新分析
          if (this.shouldReanalyze(cached, currentRounds)) {
            this.candidateTypeCache.delete(sessionId);
            return null;
          }
          return cached.data;
        } else {
          // 内存缓存过期，删除
          this.candidateTypeCache.delete(sessionId);
        }
      }

      // 2. 使用database-manager的文件缓存
      if (this.database) {
        const fileCached = await this.database.getCandidateTypeCache(sessionId);
        if (fileCached) {
          // 检查是否需要重新分析
          if (this.shouldReanalyze(fileCached, currentRounds)) {
            await this.database.removeCandidateTypeCache(sessionId);
            return null;
          }

          // 重新加载到内存缓存
          this.candidateTypeCache.set(sessionId, fileCached);
          return fileCached.data;
        }
      }

      return null;
    } catch (error) {
      console.error("❌ 获取缓存失败:", error);
      return null;
    }
  }

  /**
   * 判断是否需要重新分析
   */
  shouldReanalyze(cached, currentRounds) {
    // 1. 置信度过低
    if (cached.data.confidence < this.cacheStrategy.confidenceThreshold) {
      return true;
    }

    // 2. 轮次增长过多（每10轮重新分析）
    const roundsDiff = currentRounds - (cached.rounds || 0);
    if (roundsDiff >= this.cacheStrategy.roundsThreshold) {
      return true;
    }

    return false;
  }

  /**
   * 设置候选人类型缓存（使用database-manager）
   */
  async setCachedCandidateType(sessionId, analysis, currentRounds) {
    try {
      const cacheData = {
        data: analysis,
        timestamp: Date.now(),
        rounds: currentRounds,
        sessionId: sessionId,
      };

      // 1. 设置内存缓存
      this.candidateTypeCache.set(sessionId, cacheData);

      // 2. 使用database-manager持久化
      if (this.database) {
        await this.database.setCandidateTypeCache(
          sessionId,
          analysis,
          currentRounds
        );
      }

      // 3. 定期清理过期缓存
      if (Math.random() < 0.1) {
        await this.database?.cleanupExpiredCandidateTypeCache();
      }
    } catch (error) {
      console.error("❌ 设置缓存失败:", error);
    }
  }

  // ==================== 缓存管理已移至database-manager.js ====================

  /**
   * 从数据库获取缓存
   */
  async getFromDatabase(sessionId) {
    try {
      if (!this.database?.client) return null;

      const { data, error } = await this.database.client
        .from("candidate_type_cache")
        .select("*")
        .eq("session_id", sessionId)
        .single();

      if (error || !data) return null;

      return {
        data: data.analysis_result,
        timestamp: new Date(data.created_at).getTime(),
        rounds: data.rounds,
        sessionId: data.session_id,
      };
    } catch (error) {
      console.error("❌ 数据库缓存读取失败:", error);
      return null;
    }
  }

  /**
   * 保存到数据库
   */
  async saveToDatabase(sessionId, cacheData) {
    try {
      if (!this.database?.client) return;

      const { error } = await this.database.client
        .from("candidate_type_cache")
        .upsert({
          session_id: sessionId,
          analysis_result: cacheData.data,
          rounds: cacheData.rounds,
          created_at: new Date(cacheData.timestamp).toISOString(),
          updated_at: new Date().toISOString(),
        });

      if (error) {
        console.error("❌ 数据库缓存保存失败:", error);
      }
    } catch (error) {
      console.error("❌ 数据库缓存保存异常:", error);
    }
  }

  /**
   * 从数据库删除过期缓存
   */
  async removeFromDatabase(sessionId) {
    try {
      if (!this.database?.client) return;

      await this.database.client
        .from("candidate_type_cache")
        .delete()
        .eq("session_id", sessionId);
    } catch (error) {
      console.error("❌ 数据库缓存删除失败:", error);
    }
  }

  /**
   * 清理过期缓存（统一14天策略）
   */
  async cleanupExpiredCache() {
    try {
      const now = Date.now();
      const expiry = this.cacheStrategy.uniformExpiry;

      // 1. 清理内存缓存
      for (const [sessionId, cached] of this.candidateTypeCache.entries()) {
        if (now - cached.timestamp > expiry) {
          this.candidateTypeCache.delete(sessionId);
        }
      }

      // 2. 清理文件缓存（随机触发，避免频繁IO）
      if (this.cacheStrategy.useFileCache && Math.random() < 0.1) {
        await this.cleanupExpiredFileCache();
      }

      // 3. 限制内存缓存大小
      if (this.candidateTypeCache.size > this.cacheStrategy.maxCacheSize) {
        const entries = Array.from(this.candidateTypeCache.entries());
        entries.sort((a, b) => a[1].timestamp - b[1].timestamp);

        // 删除最老的20%
        const deleteCount = Math.floor(entries.length * 0.2);
        for (let i = 0; i < deleteCount; i++) {
          this.candidateTypeCache.delete(entries[i][0]);
        }
      }
    } catch (error) {
      console.error("❌ 缓存清理失败:", error);
    }
  }

  /**
   * 清理过期的文件缓存
   */
  async cleanupExpiredFileCache() {
    try {
      const fs = require("fs").promises;
      const path = require("path");

      const cacheDir = this.cacheStrategy.cacheDirectory;

      // 检查目录是否存在
      try {
        await fs.access(cacheDir);
      } catch {
        return; // 目录不存在，无需清理
      }

      const files = await fs.readdir(cacheDir);
      const now = Date.now();
      let cleanedCount = 0;

      for (const file of files) {
        if (!file.endsWith(".json")) continue;

        const filePath = path.join(cacheDir, file);

        try {
          const data = await fs.readFile(filePath, "utf8");
          const cacheData = JSON.parse(data);

          // 检查是否过期
          if (now - cacheData.timestamp > this.cacheStrategy.uniformExpiry) {
            await fs.unlink(filePath);
            cleanedCount++;
          }
        } catch (error) {
          // 文件损坏或无法读取，直接删除
          try {
            await fs.unlink(filePath);
            cleanedCount++;
          } catch {
            // 删除失败，忽略
          }
        }
      }

      if (cleanedCount > 0) {
        console.log(`🧹 清理了 ${cleanedCount} 个过期缓存文件`);
      }
    } catch (error) {
      console.error("❌ 文件缓存清理失败:", error);
    }
  }

  /**
   * 解析候选人类型分析结果
   */
  parseCandidateTypeResult(result) {
    try {
      const parsed = JSON.parse(result);
      return {
        type: parsed.type || "正常型",
        confidence: parsed.confidence || 0.5,
        evidence: parsed.evidence || [],
        strategy: parsed.strategy || "标准流程",
        riskLevel: parsed.riskLevel || "low",
        timestamp: Date.now(),
      };
    } catch (error) {
      console.error("❌ 解析候选人类型结果失败:", error);
      return {
        type: "正常型",
        confidence: 0.3,
        evidence: ["解析失败，使用默认类型"],
        strategy: "标准流程",
        riskLevel: "low",
        timestamp: Date.now(),
      };
    }
  }

  /**
   * 规则引擎容灾分析
   */
  fallbackCandidateTypeAnalysis(dialogueHistory) {
    const lastMessages = dialogueHistory.slice(-3);
    const userMessages = lastMessages.filter((msg) => msg.role === "user");

    if (userMessages.length === 0) {
      return {
        type: "正常型",
        confidence: 0.5,
        evidence: ["无足够对话数据"],
        strategy: "标准流程",
        riskLevel: "low",
        source: "fallback_rule_engine",
      };
    }

    // 使用行为模式分析进行容灾判断
    const behaviorAnalysis = this.analyzeBehaviorPatterns(dialogueHistory);

    let type = "正常型";
    let riskLevel = "low";
    let evidence = [];

    // 挤牙膏型判断（使用环境变量配置）
    const shortReplyThreshold =
      parseFloat(process.env.SHORT_REPLY_THRESHOLD) || 0.6;
    const avgLengthThreshold =
      parseInt(process.env.AVG_MESSAGE_LENGTH_THRESHOLD) || 8;

    if (
      behaviorAnalysis.shortReplyRatio > shortReplyThreshold &&
      behaviorAnalysis.avgMessageLength < avgLengthThreshold
    ) {
      type = "挤牙膏型";
      riskLevel = "medium";
      evidence = ["短回复比例高", "平均消息长度短"];
    }
    // 疯狂咨询型判断（使用环境变量配置）
    else {
      const questionRatioThreshold =
        parseFloat(process.env.QUESTION_RATIO_THRESHOLD) || 0.5;
      const infoProvisionThreshold =
        parseFloat(process.env.INFO_PROVISION_THRESHOLD) || 0.2;

      if (
        behaviorAnalysis.questionRatio > questionRatioThreshold &&
        behaviorAnalysis.infoProvisionRatio < infoProvisionThreshold
      ) {
        type = "疯狂咨询型";
        riskLevel = "medium";
        evidence = ["问题比例高", "信息提供少"];
      }
      // 质疑/不信任型判断
      else if (behaviorAnalysis.doubtExpressions.length > 0) {
        type = "质疑/不信任型";
        riskLevel = "high";
        evidence = ["存在质疑表达"];
      }
    }

    return {
      type,
      confidence: 0.6,
      evidence,
      strategy: type === "正常型" ? "标准流程" : "特殊处理",
      riskLevel,
      source: "fallback_rule_engine",
      behaviorPattern: behaviorAnalysis,
    };
  }

  /**
   * 生成容灾回复（使用配置模板）
   */
  generateFallbackReply(userMessage, candidateType, context = {}) {
    try {
      // 使用AppConfig中的容灾模板
      const AppConfig = require("../系统核心/app-config");
      const config = new AppConfig();

      return config.generateFallbackReply(userMessage, candidateType, context);
    } catch (error) {
      console.error("❌ 容灾回复生成失败:", error);

      // 最后的兜底回复
      return {
        content: "抱歉，系统暂时有些问题，请稍后再试。",
        source: "emergency_fallback",
        timestamp: Date.now(),
      };
    }
  }

  /**
   * 获取服务统计信息
   */
  getStats() {
    return {
      initialized: this.isInitialized,
      endpoint: this.apiEndpoint,
      maxTokens: this.maxTokens,
      temperature: this.temperature,
      queue: {
        pending: this.requestQueue.length,
        processing: this.currentRequests,
        maxConcurrent: this.maxConcurrentRequests,
      },
      performance: {
        ...this.stats,
        successRate:
          this.stats.totalRequests > 0
            ? (
                (this.stats.successfulRequests / this.stats.totalRequests) *
                100
              ).toFixed(2) + "%"
            : "0%",
      },
      // 添加API调用统计
      qwenCalls: this.stats.qwenCalls,
      deepseekCalls: this.stats.deepseekCalls,
      cacheHits: this.stats.cacheHits,
    };
  }

  /**
   * 获取队列状态
   */
  getQueueStatus() {
    return {
      pending: this.requestQueue.length,
      processing: this.currentRequests,
      maxConcurrent: this.maxConcurrentRequests,
      isHealthy:
        this.requestQueue.length < 100 &&
        this.currentRequests < this.maxConcurrentRequests,
    };
  }

  // ==================== 监控和容灾模块 ====================

  /**
   * 更新费用跟踪
   */
  updateCostTracking(modelType, inputTokens, outputTokens) {
    const today = new Date().toDateString();

    // 重置日费用（新的一天）
    if (this.costMonitor.lastResetDate !== today) {
      this.costMonitor.currentDailyCost = 0;
      this.costMonitor.lastResetDate = today;
      this.costMonitor.isBlocked = false;
    }

    // 计算本次调用费用
    let cost = 0;
    if (modelType === "qwen") {
      cost = (inputTokens * 0.3 + outputTokens * 0.6) / 1000000; // 元
    } else if (modelType === "deepseek") {
      cost = (inputTokens * 2 + outputTokens * 8) / 1000000; // 元
    }

    this.costMonitor.currentDailyCost += cost;

    // 检查费用告警
    const usageRatio =
      this.costMonitor.currentDailyCost / this.costMonitor.dailyLimit;
    if (
      usageRatio >= this.costMonitor.alertThreshold &&
      !this.costMonitor.isBlocked
    ) {
      console.warn(
        `⚠️ 费用告警: 已使用 ${(usageRatio * 100).toFixed(1)}% (${this.costMonitor.currentDailyCost.toFixed(2)}元/${this.costMonitor.dailyLimit}元)`
      );
    }

    // 超过限制时阻止调用
    if (usageRatio >= 1.0) {
      this.costMonitor.isBlocked = true;
      console.error(
        `🚫 费用超限: ${this.costMonitor.currentDailyCost.toFixed(2)}元，已阻止新的API调用`
      );
    }

    return {
      cost,
      dailyTotal: this.costMonitor.currentDailyCost,
      usageRatio,
      isBlocked: this.costMonitor.isBlocked,
    };
  }

  /**
   * 处理模型错误
   */
  handleModelError(modelType, error) {
    const health = this.modelHealth[modelType];
    if (!health) return;

    health.consecutiveFailures++;
    health.lastError = {
      message: error.message,
      timestamp: Date.now(),
      code: error.code || "UNKNOWN",
    };

    // 连续失败超过阈值时标记为不健康
    if (health.consecutiveFailures >= health.maxFailures) {
      health.isHealthy = false;
      console.error(
        `❌ ${modelType}模型标记为不健康，连续失败${health.consecutiveFailures}次`
      );

      // 发送告警通知（可以集成钉钉、邮件等）
      this.sendHealthAlert(modelType, health);
    }
  }

  /**
   * 模型健康检查
   */
  async checkModelHealth(modelType) {
    try {
      const testPrompt = "Hello";
      let result;

      if (modelType === "qwen") {
        result = await this.callQwenTurbo(testPrompt, { maxTokens: 10 });
      } else if (modelType === "deepseek") {
        result = await this.generateResponse(testPrompt, { maxTokens: 10 });
      }

      if (result) {
        // 健康检查成功，重置失败计数
        this.modelHealth[modelType].consecutiveFailures = 0;
        this.modelHealth[modelType].isHealthy = true;
        this.modelHealth[modelType].lastCheck = Date.now();
        console.log(`✅ ${modelType}模型健康检查通过`);
        return true;
      }
    } catch (error) {
      console.error(`❌ ${modelType}模型健康检查失败:`, error);
      this.handleModelError(modelType, error);
      return false;
    }
  }

  /**
   * 发送健康告警
   */
  sendHealthAlert(modelType, health) {
    const alertData = {
      type: "MODEL_HEALTH_ALERT",
      modelType,
      consecutiveFailures: health.consecutiveFailures,
      lastError: health.lastError,
      timestamp: new Date().toISOString(),
    };

    console.error("🚨 模型健康告警:", alertData);

    // TODO: 集成实际的告警系统（钉钉、邮件、短信等）
    // this.notificationService.sendAlert(alertData);
  }

  /**
   * 获取监控状态
   */
  getMonitoringStatus() {
    return {
      modelHealth: this.modelHealth,
      costMonitor: {
        ...this.costMonitor,
        usageRatio:
          this.costMonitor.currentDailyCost / this.costMonitor.dailyLimit,
      },
      stats: this.stats,
      cacheStats: {
        size: this.candidateTypeCache.size,
        hitRate:
          this.stats.cacheHits /
            (this.stats.qwenCalls + this.stats.cacheHits) || 0,
      },
    };
  }

  // ==================== 智能回复路由（已移至utilities.js） ====================

  /**
   * 使用Turbo生成简单回复
   */
  async generateSimpleReplyWithTurbo(messageContext) {
    const { userMessage, candidateType } = messageContext;

    const prompt = `作为AI招聘助手，针对候选人的简单消息进行回复。
候选人类型：${candidateType?.type || "正常型"}
候选人消息：${userMessage}

要求：
1. 回复简洁友好（20字以内）
2. 保持专业性
3. 适当引导对话

回复：`;

    try {
      const response = await this.callQwenTurbo(prompt, { maxTokens: 100 });
      this.stats.qwenCalls++;
      this.updateCostTracking("qwen", prompt.length, response.length);
      return response;
    } catch (error) {
      console.error("❌ Turbo简单回复失败:", error);
      throw error;
    }
  }

  /**
   * 调用Qwen-Turbo模型（真实API接入）
   */
  async callQwenTurbo(prompt, options = {}) {
    try {
      // 检查环境变量
      if (!process.env.QWEN_API_KEY) {
        console.warn("⚠️ QWEN_API_KEY未配置，使用模拟结果");
        return this.getMockQwenResponse(prompt);
      }

      const axios = require("axios");

      const requestData = {
        model: process.env.QWEN_MODEL || "qwen-turbo",
        input: {
          messages: [{ role: "user", content: prompt }],
        },
        parameters: {
          max_tokens: options.maxTokens || 200,
          temperature: options.temperature || 0.1,
          top_p: 0.8,
          result_format: "message",
        },
      };

      const response = await axios.post(
        process.env.QWEN_ENDPOINT ||
          "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation",
        requestData,
        {
          headers: {
            Authorization: `Bearer ${process.env.QWEN_API_KEY}`,
            "Content-Type": "application/json",
            "X-DashScope-SSE": "disable",
          },
          timeout: this.timeout,
        }
      );

      if (
        response.data.output &&
        response.data.output.choices &&
        response.data.output.choices[0]
      ) {
        const result = response.data.output.choices[0].message.content;
        console.log("✅ Qwen-Turbo调用成功");
        return result;
      } else {
        throw new Error("Qwen API响应格式异常");
      }
    } catch (error) {
      console.error("❌ Qwen-Turbo调用失败:", error.message);

      // 容错：返回模拟结果
      return this.getMockQwenResponse(prompt);
    }
  }

  /**
   * 获取模拟的Qwen响应（容错机制）
   */
  getMockQwenResponse(prompt) {
    console.log("🔄 使用Qwen模拟响应:", prompt.substring(0, 50) + "...");

    // 如果是分析类型的prompt，返回分析结果
    if (prompt.includes("候选人类型")) {
      return JSON.stringify({
        type: "正常型",
        confidence: 0.8,
        evidence: ["模拟分析结果"],
        strategy: "标准流程",
        riskLevel: "low",
      });
    }

    // 如果是回复类型的prompt，返回简单回复
    return "好的，我了解了。请问您的技术方向是什么呢？";
  }
}

module.exports = AIServices;
