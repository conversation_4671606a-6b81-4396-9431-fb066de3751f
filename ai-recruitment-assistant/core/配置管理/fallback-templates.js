/**
 * 容灾回复模板
 * 
 * 当AI模型不可用时使用的备用回复模板
 */

class FallbackTemplates {
  constructor() {
    // 基础回复模板
    this.basicTemplates = {
      greeting: [
        "您好！我是AI招聘助手Katrina，很高兴为您服务。",
        "您好！感谢您的关注，我是专注AI领域的猎头Katrina。"
      ],
      
      infoRequest: [
        "为了给您推荐更合适的职位，能否告诉我您的技术方向、当前公司、职级和期望薪资呢？",
        "请问您的技术栈是什么？目前在哪家公司？这样我能更精准地为您推荐职位。"
      ],
      
      confirmation: [
        "好的，我了解了。",
        "明白，感谢您的信息。",
        "收到，让我为您查看合适的职位。"
      ],
      
      jobInquiry: [
        "我们合作了很多优质公司，包括头部大厂、中型公司、创业团队等。需要先了解您的背景才能精准推荐。",
        "职位确实很多，但需要根据您的具体情况来匹配。请问您的技术方向是什么？"
      ],
      
      decline: [
        "好的，理解您的想法。如果未来有需要，欢迎随时联系我。",
        "没关系，祝您工作顺利！有机会再合作。"
      ]
    };

    // 候选人类型特定模板
    this.typeSpecificTemplates = {
      "挤牙膏型": {
        patience: [
          "没关系，您可以慢慢考虑。我这边有很多不错的机会。",
          "理解您需要时间思考，不着急。请问您主要做什么技术方向的？",
          "好的，那我换个问题，您目前的工作年限大概是多少呢？"
        ],
        gentle_push: [
          "为了不浪费您的时间，能简单告诉我您的技术栈吗？",
          "我这边确实有一些不错的机会，但需要了解您的基本情况才能推荐。",
          "您看这样，您先告诉我您的技术方向，我看看有没有合适的？"
        ]
      },
      
      "疯狂咨询型": {
        boundary: [
          "我理解您想了解更多，但我们有规定，需要先了解候选人基本信息才能推荐职位。",
          "抱歉，为了保护双方利益，我需要先了解您的背景再进行推荐。",
          "我们的流程是这样的，先了解您的情况，再匹配合适的职位。"
        ],
        firm: [
          "请您配合提供基本信息，否则我无法为您推荐合适的职位。",
          "如果您不愿意提供基本信息，我可能无法继续为您服务。",
          "我们需要相互配合才能找到最合适的机会。"
        ]
      },
      
      "质疑/不信任型": {
        trust_building: [
          "我理解您的担心。我们是正规的招聘公司，所有职位都是真实有效的。",
          "我们的职位信息每周都会更新，确保真实性。您可以先了解一下我们公司。",
          "职位的真实性我可以保证，我们与这些公司都有正式的合作协议。"
        ],
        evidence: [
          "我可以提供公司的详细信息和职位描述，您可以先看看是否感兴趣。",
          "如果您担心，我们可以先电话沟通，我详细介绍一下具体情况。",
          "我们公司在行业内有很好的口碑，您可以查询一下我们的资质。"
        ]
      }
    };

    // 紧急情况模板
    this.emergencyTemplates = {
      system_error: "抱歉，系统暂时有些问题，请稍后再试或者留下联系方式，我稍后回复您。",
      api_limit: "当前咨询量较大，请稍等片刻，或者您可以留下微信，我稍后详细为您介绍。",
      unknown_error: "抱歉出现了一些技术问题，请问您方便留个联系方式吗？我稍后为您详细介绍。"
    };
  }

  /**
   * 根据场景获取回复模板
   */
  getTemplate(scenario, candidateType = null, context = {}) {
    try {
      // 1. 优先使用候选人类型特定模板
      if (candidateType && this.typeSpecificTemplates[candidateType]) {
        const typeTemplates = this.typeSpecificTemplates[candidateType];
        if (typeTemplates[scenario]) {
          return this.randomSelect(typeTemplates[scenario]);
        }
      }

      // 2. 使用基础模板
      if (this.basicTemplates[scenario]) {
        return this.randomSelect(this.basicTemplates[scenario]);
      }

      // 3. 紧急情况模板
      if (this.emergencyTemplates[scenario]) {
        return this.emergencyTemplates[scenario];
      }

      // 4. 默认回复
      return this.getDefaultReply(context);

    } catch (error) {
      console.error("❌ 获取模板失败:", error);
      return "抱歉，请稍后再试。";
    }
  }

  /**
   * 随机选择模板
   */
  randomSelect(templates) {
    if (!Array.isArray(templates) || templates.length === 0) {
      return "抱歉，请稍后再试。";
    }
    const index = Math.floor(Math.random() * templates.length);
    return templates[index];
  }

  /**
   * 获取默认回复
   */
  getDefaultReply(context = {}) {
    const { messageCount = 0, hasPersonalInfo = false } = context;
    
    if (messageCount < 3) {
      return "您好！请问您考虑看看新的工作机会吗？";
    }
    
    if (!hasPersonalInfo) {
      return "为了给您推荐合适的职位，能否告诉我您的技术方向和期望呢？";
    }
    
    return "好的，让我为您查看一下合适的职位。";
  }

  /**
   * 智能场景识别
   */
  identifyScenario(userMessage, context = {}) {
    const message = userMessage.toLowerCase();
    
    // 问候语
    if (/你好|您好|hi|hello/.test(message)) {
      return 'greeting';
    }
    
    // 职位咨询
    if (/职位|工作|推荐|有什么|有哪些/.test(message)) {
      return 'jobInquiry';
    }
    
    // 拒绝
    if (/不需要|不考虑|算了|谢谢|再说/.test(message)) {
      return 'decline';
    }
    
    // 简单确认
    if (/^(嗯|好的|可以|行|是的|对)$/.test(message.trim())) {
      return 'confirmation';
    }
    
    // 信息请求
    if (context.needsInfo) {
      return 'infoRequest';
    }
    
    return 'default';
  }

  /**
   * 生成容灾回复
   */
  generateFallbackReply(userMessage, candidateType, context = {}) {
    const scenario = this.identifyScenario(userMessage, context);
    const reply = this.getTemplate(scenario, candidateType, context);
    
    console.log(`🔄 使用容灾模板: ${scenario} -> ${reply.substring(0, 30)}...`);
    
    return {
      content: reply,
      source: 'fallback_template',
      scenario: scenario,
      candidateType: candidateType,
      timestamp: Date.now()
    };
  }

  /**
   * 获取所有可用场景
   */
  getAvailableScenarios() {
    return {
      basic: Object.keys(this.basicTemplates),
      typeSpecific: Object.keys(this.typeSpecificTemplates),
      emergency: Object.keys(this.emergencyTemplates)
    };
  }
}

module.exports = FallbackTemplates;
