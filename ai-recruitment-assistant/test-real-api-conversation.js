/**
 * 真实API对话测试 - 从开场白开始
 * 调用DeepSeek V3和Qwen-Turbo进行真实的一问一答测试
 * Qwen: 分析和识别, DeepSeek: 推理回复
 */

require("dotenv").config({ path: "../.env" });

const DatabaseManager = require("./core/数据管理/database-manager");
const MessageProcessor = require("./core/系统核心/message-processor");
const UserManager = require("./core/业务服务/user-manager");
const AppConfig = require("./core/系统核心/app-config");

class RealAPIConversationTest {
  constructor() {
    this.realUserId = 623;
    this.sessionId = "real-api-" + Date.now();
    this.conversationHistory = [];

    // API配置
    this.deepseekConfig = {
      apiKey: process.env.DEEPSEEK_API_KEY,
      endpoint: process.env.DEEPSEEK_ENDPOINT,
      model: "deepseek-chat",
    };

    this.qwenConfig = {
      apiKey: process.env.QWEN_API_KEY,
      endpoint: process.env.QWEN_ENDPOINT,
      model: "qwen-turbo",
    };

    // 挤牙膏式对话场景
    this.conversationSteps = [
      {
        step: 1,
        userMessage: "你好",
        description: "开场白 - 用户初次接触",
      },
      {
        step: 2,
        userMessage: "我想找工作",
        description: "表达求职意向",
      },
      {
        step: 3,
        userMessage: "算法方面的",
        description: "提供模糊的技术信息",
      },
      {
        step: 4,
        userMessage: "推荐算法，我在腾讯",
        description: "提供具体技术方向和公司",
      },
      {
        step: 5,
        userMessage: "10级",
        description: "提供职级信息",
      },
      {
        step: 6,
        userMessage: "期望150万年薪",
        description: "提供薪资期望",
      },
      {
        step: 7,
        userMessage: "有没有大厂的机会？",
        description: "询问特定类型职位",
      },
    ];
  }

  async runRealAPIConversation() {
    console.log("🤖 开始真实API对话测试\n");
    console.log(`📅 测试时间: ${new Date().toISOString()}`);
    console.log(`👤 测试用户: ID ${this.realUserId}`);
    console.log(
      `🔑 DeepSeek API: ${this.deepseekConfig.apiKey ? "已配置" : "未配置"}`
    );
    console.log(
      `🔑 Qwen API: ${this.qwenConfig.apiKey ? "已配置" : "未配置"}\n`
    );

    for (const step of this.conversationSteps) {
      await this.processConversationStep(step);
      await this.sleep(2000); // 模拟真实对话间隔
    }

    this.generateConversationReport();
  }

  async processConversationStep(step) {
    console.log(`\n📝 第${step.step}步: ${step.description}`);
    console.log(`👤 用户: "${step.userMessage}"`);

    // 构建对话上下文
    const conversationContext = this.buildConversationContext();

    // 调用DeepSeek进行意图分析
    const intentAnalysis = await this.callDeepSeekForIntent(
      step.userMessage,
      conversationContext
    );

    // 调用Qwen生成回复
    const aiResponse = await this.callQwenForResponse(
      step.userMessage,
      conversationContext,
      intentAnalysis
    );

    // 记录对话历史
    this.conversationHistory.push({
      step: step.step,
      userMessage: step.userMessage,
      intentAnalysis: intentAnalysis,
      aiResponse: aiResponse,
      timestamp: new Date().toISOString(),
    });

    console.log(
      `🧠 DeepSeek意图分析: ${intentAnalysis.success ? intentAnalysis.analysis : "分析失败"}`
    );
    console.log(
      `🤖 Qwen回复: ${aiResponse.success ? aiResponse.response : "回复失败"}`
    );
  }

  buildConversationContext() {
    if (this.conversationHistory.length === 0) {
      return "这是对话的开始。";
    }

    let context = "对话历史：\n";
    this.conversationHistory.forEach((turn, i) => {
      context += `${i + 1}. 用户: ${turn.userMessage}\n`;
      if (turn.aiResponse.success) {
        context += `   助手: ${turn.aiResponse.response}\n`;
      }
    });
    return context;
  }

  async callDeepSeekForIntent(userMessage, context) {
    const startTime = Date.now();

    try {
      const systemPrompt = `你是一个招聘助手的意图分析模块。请分析用户消息的意图，提取关键信息（公司、技术方向、职级、薪资、城市等），判断是否需要推荐职位。

对话上下文：
${context}

请用JSON格式回复，包含：
{
  "intent": "用户意图",
  "extractedInfo": {
    "company": "公司名称",
    "techDirection": "技术方向", 
    "level": "职级",
    "salary": "薪资",
    "city": "城市"
  },
  "needsRecommendation": true/false,
  "nextAction": "下一步建议"
}`;

      const response = await axios.post(
        this.deepseekConfig.endpoint,
        {
          model: this.deepseekConfig.model,
          messages: [
            { role: "system", content: systemPrompt },
            { role: "user", content: userMessage },
          ],
          max_tokens: 500,
          temperature: 0.3,
        },
        {
          headers: {
            Authorization: `Bearer ${this.deepseekConfig.apiKey}`,
            "Content-Type": "application/json",
          },
          timeout: 30000,
        }
      );

      const duration = Date.now() - startTime;
      const analysis = response.data.choices[0].message.content;

      console.log(`⏱️ DeepSeek响应时间: ${duration}ms`);

      return {
        success: true,
        analysis: analysis,
        duration: duration,
        tokens: response.data.usage?.total_tokens || 0,
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      console.log(`❌ DeepSeek调用失败: ${error.message} (${duration}ms)`);

      return {
        success: false,
        error: error.message,
        duration: duration,
      };
    }
  }

  async callQwenForResponse(userMessage, context, intentAnalysis) {
    const startTime = Date.now();

    try {
      const systemPrompt = `你是一个专业的招聘助手。根据用户消息和意图分析，生成合适的回复。

对话上下文：
${context}

意图分析结果：
${intentAnalysis.success ? intentAnalysis.analysis : "意图分析失败"}

请生成自然、专业的回复，引导用户提供更多信息或提供职位推荐。回复要简洁明了，不超过100字。`;

      const response = await axios.post(
        this.qwenConfig.endpoint,
        {
          model: this.qwenConfig.model,
          input: {
            messages: [
              { role: "system", content: systemPrompt },
              { role: "user", content: userMessage },
            ],
          },
          parameters: {
            max_tokens: 300,
            temperature: 0.7,
            result_format: "message",
          },
        },
        {
          headers: {
            Authorization: `Bearer ${this.qwenConfig.apiKey}`,
            "Content-Type": "application/json",
            "X-DashScope-SSE": "disable",
          },
          timeout: 30000,
        }
      );

      const duration = Date.now() - startTime;
      const aiResponse =
        response.data.output?.choices?.[0]?.message?.content ||
        response.data.output?.text ||
        "No response";

      console.log(`⏱️ Qwen响应时间: ${duration}ms`);

      return {
        success: true,
        response: aiResponse,
        duration: duration,
        tokens: response.data.usage?.total_tokens || 0,
      };
    } catch (error) {
      const duration = Date.now() - startTime;
      console.log(`❌ Qwen调用失败: ${error.message} (${duration}ms)`);

      return {
        success: false,
        error: error.message,
        duration: duration,
      };
    }
  }

  generateConversationReport() {
    console.log(
      "\n📊 ==================== 真实API对话测试报告 ===================="
    );

    const totalSteps = this.conversationHistory.length;
    const successfulDeepSeek = this.conversationHistory.filter(
      (h) => h.intentAnalysis.success
    ).length;
    const successfulQwen = this.conversationHistory.filter(
      (h) => h.aiResponse.success
    ).length;

    console.log(`💬 总对话轮数: ${totalSteps}`);
    console.log(
      `🧠 DeepSeek成功率: ${successfulDeepSeek}/${totalSteps} (${((successfulDeepSeek / totalSteps) * 100).toFixed(1)}%)`
    );
    console.log(
      `🤖 Qwen成功率: ${successfulQwen}/${totalSteps} (${((successfulQwen / totalSteps) * 100).toFixed(1)}%)`
    );

    // 计算平均响应时间
    const deepSeekTimes = this.conversationHistory
      .map((h) => h.intentAnalysis.duration)
      .filter((d) => d);
    const qwenTimes = this.conversationHistory
      .map((h) => h.aiResponse.duration)
      .filter((d) => d);

    if (deepSeekTimes.length > 0) {
      const avgDeepSeek =
        deepSeekTimes.reduce((a, b) => a + b, 0) / deepSeekTimes.length;
      console.log(`⏱️ DeepSeek平均响应时间: ${avgDeepSeek.toFixed(0)}ms`);
    }

    if (qwenTimes.length > 0) {
      const avgQwen = qwenTimes.reduce((a, b) => a + b, 0) / qwenTimes.length;
      console.log(`⏱️ Qwen平均响应时间: ${avgQwen.toFixed(0)}ms`);
    }

    // 详细对话记录
    console.log("\n📝 详细对话记录:");
    this.conversationHistory.forEach((turn, i) => {
      console.log(`\n${i + 1}. 👤 "${turn.userMessage}"`);

      if (turn.intentAnalysis.success) {
        console.log(
          `   🧠 DeepSeek分析: ${turn.intentAnalysis.analysis.substring(0, 100)}...`
        );
      } else {
        console.log(`   ❌ DeepSeek失败: ${turn.intentAnalysis.error}`);
      }

      if (turn.aiResponse.success) {
        console.log(`   🤖 Qwen回复: ${turn.aiResponse.response}`);
      } else {
        console.log(`   ❌ Qwen失败: ${turn.aiResponse.error}`);
      }
    });

    console.log("\n🎯 测试结论:");
    if (successfulDeepSeek === totalSteps && successfulQwen === totalSteps) {
      console.log("✅ 所有API调用成功，双模型对话系统运行正常");
    } else {
      console.log("⚠️ 部分API调用失败，需要检查失败原因");
    }
  }

  sleep(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}

// 运行测试
async function runTest() {
  const test = new RealAPIConversationTest();
  await test.runRealAPIConversation();
}

if (require.main === module) {
  runTest();
}

module.exports = RealAPIConversationTest;
