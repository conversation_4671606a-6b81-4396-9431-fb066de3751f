/**
 * 真实AI模型对话测试
 * 调用DeepSeek V3和Qwen-Turbo进行真实的一问一答测试
 */

require("dotenv").config({ path: "../.env" });

const DatabaseManager = require('./core/数据管理/database-manager');
const AIServices = require('./core/数据管理/ai-services');
const MessageProcessor = require('./core/系统核心/message-processor');
const UserManager = require('./core/业务服务/user-manager');

class RealAIConversationTest {
  constructor() {
    this.realUserId = 623; // 真实用户ID
    this.testSessionId = 'ai-conversation-' + Date.now();
    this.conversationHistory = [];
    
    // 真实对话场景 - 挤牙膏式信息收集
    this.conversationScenario = [
      {
        step: 1,
        userMessage: "你好",
        description: "用户初次接触，测试AI问候和引导"
      },
      {
        step: 2,
        userMessage: "我想找工作",
        description: "表达求职意向，测试AI如何引导信息收集"
      },
      {
        step: 3,
        userMessage: "算法相关的",
        description: "提供模糊技术信息，测试AI如何深入询问"
      },
      {
        step: 4,
        userMessage: "推荐算法，我在腾讯",
        description: "提供具体信息，测试AI如何继续收集"
      },
      {
        step: 5,
        userMessage: "10级",
        description: "提供职级信息，测试AI如何处理职级映射"
      },
      {
        step: 6,
        userMessage: "期望150万年薪",
        description: "提供薪资期望，测试是否触发推荐"
      },
      {
        step: 7,
        userMessage: "有没有大厂的机会？",
        description: "被动推荐请求，测试AI推荐能力"
      }
    ];
  }

  async initialize() {
    try {
      console.log('🚀 初始化真实AI对话测试...');
      
      // 初始化数据库
      const dbConfig = {
        supabaseUrl: process.env.SUPABASE_URL,
        supabaseKey: process.env.SUPABASE_ANON_KEY,
      };
      
      this.database = new DatabaseManager(dbConfig);
      await this.database.connect();
      
      // 初始化AI服务
      const aiConfig = {
        deepseekApiKey: process.env.DEEPSEEK_API_KEY,
        deepseekEndpoint: process.env.DEEPSEEK_ENDPOINT,
        qwenApiKey: process.env.QWEN_API_KEY,
        qwenEndpoint: process.env.QWEN_ENDPOINT,
        maxTokens: 1000,
        temperature: 0.7,
        timeout: 30000
      };
      
      this.aiServices = new AIServices(aiConfig);
      await this.aiServices.initialize();
      
      // 初始化消息处理器
      this.messageProcessor = new MessageProcessor(
        this.database,
        this.aiServices,
        null, // chatInterface
        {}   // config
      );
      
      // 初始化用户管理器
      this.userManager = new UserManager(this.database, {});
      await this.userManager.initialize();
      
      console.log('✅ 系统初始化完成\n');
    } catch (error) {
      console.error('❌ 初始化失败:', error);
      throw error;
    }
  }

  async runRealAIConversation() {
    try {
      console.log('🤖 开始真实AI模型对话测试\n');
      console.log(`👤 测试用户: ID ${this.realUserId}`);
      console.log(`📅 测试时间: ${new Date().toISOString()}`);
      console.log(`🔑 DeepSeek API: ${process.env.DEEPSEEK_API_KEY ? '已配置' : '未配置'}`);
      console.log(`🔑 Qwen API: ${process.env.QWEN_API_KEY ? '已配置' : '未配置'}\n`);

      for (const scenario of this.conversationScenario) {
        await this.processRealAIStep(scenario);
        
        // 每步之间停顿，模拟真实对话节奏
        await this.sleep(2000);
      }

      console.log('\n🎉 AI对话测试完成！');
      this.generateAIConversationSummary();

    } catch (error) {
      console.error('❌ AI对话测试失败:', error);
    }
  }

  async processRealAIStep(scenario) {
    console.log(`\n📝 第${scenario.step}步: ${scenario.description}`);
    console.log(`👤 用户: "${scenario.userMessage}"`);
    
    const stepStartTime = Date.now();
    
    try {
      // 1. 调用真实的消息处理器（包含AI分析）
      console.log('🤖 调用AI模型分析...');
      
      const messageData = {
        message: scenario.userMessage,
        userId: this.realUserId,
        sessionId: this.testSessionId
      };
      
      // 调用真实的消息处理流程
      const aiResponse = await this.messageProcessor.processMessage(messageData);
      
      const stepDuration = Date.now() - stepStartTime;
      
      // 记录对话历史
      this.conversationHistory.push({
        step: scenario.step,
        userMessage: scenario.userMessage,
        aiResponse: aiResponse,
        duration: stepDuration,
        timestamp: new Date().toISOString()
      });
      
      console.log(`🤖 AI回复: ${aiResponse.response || aiResponse.message || '无回复'}`);
      console.log(`⏱️ AI处理时间: ${stepDuration}ms`);
      
      // 显示AI分析结果
      if (aiResponse.analysis) {
        console.log(`🧠 AI分析结果:`);
        console.log(`   意图: ${aiResponse.analysis.intent || '未识别'}`);
        console.log(`   置信度: ${aiResponse.analysis.confidence || 'N/A'}`);
        console.log(`   提取信息: ${JSON.stringify(aiResponse.analysis.extractedInfo || {})}`);
      }
      
      // 显示推荐结果
      if (aiResponse.recommendations && aiResponse.recommendations.length > 0) {
        console.log(`💼 AI推荐职位:`);
        aiResponse.recommendations.forEach((rec, i) => {
          console.log(`   ${i+1}. ${rec.job_title} - ${rec.company_name} (匹配度: ${rec.match_score}%)`);
        });
      }
      
    } catch (error) {
      console.error(`❌ 第${scenario.step}步AI处理失败:`, error.message);
      
      // 记录错误
      this.conversationHistory.push({
        step: scenario.step,
        userMessage: scenario.userMessage,
        error: error.message,
        duration: Date.now() - stepStartTime,
        timestamp: new Date().toISOString()
      });
    }
  }

  generateAIConversationSummary() {
    console.log('\n📊 ==================== AI对话测试总结 ====================');
    
    const totalSteps = this.conversationHistory.length;
    const successfulSteps = this.conversationHistory.filter(step => !step.error).length;
    const failedSteps = totalSteps - successfulSteps;
    
    console.log(`💬 总对话轮数: ${totalSteps}`);
    console.log(`✅ 成功处理: ${successfulSteps}`);
    console.log(`❌ 处理失败: ${failedSteps}`);
    
    if (successfulSteps > 0) {
      const totalDuration = this.conversationHistory
        .filter(step => !step.error)
        .reduce((sum, step) => sum + step.duration, 0);
      const avgDuration = totalDuration / successfulSteps;
      
      console.log(`⏱️ 总AI处理时间: ${totalDuration}ms`);
      console.log(`📈 平均AI响应时间: ${avgDuration.toFixed(0)}ms`);
    }
    
    // AI模型使用情况
    console.log('\n🤖 AI模型使用情况:');
    console.log(`   DeepSeek V3: ${this.aiServices.modelHealth?.deepseek?.isHealthy ? '✅ 正常' : '❌ 异常'}`);
    console.log(`   Qwen-Turbo: ${this.aiServices.modelHealth?.qwen?.isHealthy ? '✅ 正常' : '❌ 异常'}`);
    
    // 详细对话记录
    console.log('\n📝 详细AI对话记录:');
    this.conversationHistory.forEach((step, i) => {
      console.log(`\n${i+1}. 👤 "${step.userMessage}"`);
      
      if (step.error) {
        console.log(`   ❌ AI处理失败: ${step.error}`);
      } else {
        const response = step.aiResponse?.response || step.aiResponse?.message || '无回复';
        console.log(`   🤖 "${response.substring(0, 100)}${response.length > 100 ? '...' : ''}"`);
        
        if (step.aiResponse?.analysis) {
          console.log(`   🧠 意图: ${step.aiResponse.analysis.intent || '未识别'}`);
        }
        
        if (step.aiResponse?.recommendations?.length > 0) {
          console.log(`   💼 推荐: ${step.aiResponse.recommendations.length}个职位`);
        }
      }
      
      console.log(`   ⏱️ ${step.duration}ms`);
    });
    
    // 测试结论
    console.log('\n🎯 测试结论:');
    if (failedSteps === 0) {
      console.log('✅ 所有AI调用成功，系统运行正常');
    } else if (successfulSteps > failedSteps) {
      console.log('⚠️ 部分AI调用失败，需要检查API配置或网络连接');
    } else {
      console.log('❌ 大部分AI调用失败，系统存在严重问题');
    }
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 运行测试
async function runRealAITest() {
  const test = new RealAIConversationTest();
  
  try {
    await test.initialize();
    await test.runRealAIConversation();
  } catch (error) {
    console.error('❌ AI对话测试执行失败:', error);
  }
}

if (require.main === module) {
  runRealAITest();
}

module.exports = RealAIConversationTest;
