/**
 * 系统功能测试脚本
 * 测试双模型架构和14天缓存功能
 */

const AIServices = require("./ai-recruitment-assistant/core/数据管理/ai-services");
const DatabaseManager = require("./ai-recruitment-assistant/core/数据管理/database-manager");
const Utilities = require("./ai-recruitment-assistant/core/工具库/utilities");
const AppConfig = require("./ai-recruitment-assistant/core/系统核心/app-config");

// 模拟配置
const testConfig = {
  deepseekEndpoint:
    process.env.DEEPSEEK_ENDPOINT || "https://api.deepseek.com/v1",
  deepseekApiKey: process.env.DEEPSEEK_API_KEY || "test-key",
  qwenEndpoint:
    process.env.QWEN_ENDPOINT ||
    "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation",
  qwenApiKey: process.env.QWEN_API_KEY || "test-key",
  supabaseUrl: process.env.SUPABASE_URL || "test-url",
  supabaseKey: process.env.SUPABASE_ANON_KEY || "test-key",
  maxTokens: 2000,
  temperature: 0.7,
  timeout: 30000,
  dailyCostLimit: 50,
  database: {
    supabaseUrl: process.env.SUPABASE_URL || "test-url",
    supabaseKey: process.env.SUPABASE_ANON_KEY || "test-key",
  },
  ai: {
    deepseekEndpoint:
      process.env.DEEPSEEK_ENDPOINT || "https://api.deepseek.com/v1",
    deepseekApiKey: process.env.DEEPSEEK_API_KEY || "test-key",
    qwenEndpoint:
      process.env.QWEN_ENDPOINT ||
      "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation",
    qwenApiKey: process.env.QWEN_API_KEY || "test-key",
  },
};

async function testSystem() {
  console.log("🚀 开始系统功能测试...\n");

  // 1. 测试工具函数
  console.log("🔧 测试工具函数...");
  const utils = new Utilities();

  const testMessage = "你好，我是算法工程师，在腾讯工作3年，期望薪资50万";
  console.log(`消息: "${testMessage}"`);
  console.log(
    `包含个人信息: ${utils.containsPersonalInfo(testMessage) ? "✅" : "❌"}`
  );
  console.log(`是问候语: ${utils.isGreeting(testMessage) ? "✅" : "❌"}`);

  // 2. 测试AI服务
  console.log("\n🤖 测试AI服务...");
  const aiServices = new AIServices(testConfig);
  await aiServices.initialize();

  const testDialogue = [
    { role: "assistant", content: "您好，我是AI招聘助手Katrina" },
    { role: "user", content: "你好" },
    { role: "assistant", content: "您考虑看看新机会吗？" },
    { role: "user", content: "嗯" },
  ];

  // 测试候选人类型分析
  const analysis = await aiServices.analyzeCandidateType(
    testDialogue,
    "test-session-001"
  );
  console.log(
    `候选人类型分析: ${analysis.type} (置信度: ${analysis.confidence})`
  );

  // 测试缓存
  const cachedAnalysis = await aiServices.analyzeCandidateType(
    testDialogue,
    "test-session-001"
  );
  console.log(
    `缓存测试: ${cachedAnalysis === analysis ? "✅ 命中" : "❌ 未命中"}`
  );

  // 3. 测试数据库管理器缓存
  console.log("\n💾 测试数据库缓存管理...");
  const dbManager = new DatabaseManager(testConfig);

  await dbManager.setCandidateTypeCache("test-session-002", analysis, 4);
  const dbCached = await dbManager.getCandidateTypeCache("test-session-002");
  console.log(`数据库缓存: ${dbCached ? "✅ 保存成功" : "❌ 保存失败"}`);

  // 4. 测试容灾模板
  console.log("\n🛡️ 测试容灾模板...");
  try {
    const appConfig = new AppConfig(testConfig);
    await appConfig.initialize();

    const fallbackReply = appConfig.generateFallbackReply("你好", "正常型", {
      messageCount: 1,
    });
    console.log(`容灾回复: ${fallbackReply.content}`);
    console.log(`回复场景: ${fallbackReply.scenario}`);
  } catch (error) {
    console.log("⚠️ 容灾模板测试跳过（配置问题）");
  }

  // 5. 测试费用监控
  console.log("\n💰 测试费用监控...");

  // 模拟API调用
  for (let i = 0; i < 5; i++) {
    aiServices.updateCostTracking("qwen", 200, 50);
    aiServices.updateCostTracking("deepseek", 200, 100);
  }

  const monitoringStatus = aiServices.getMonitoringStatus();
  console.log(
    `当前费用: ${monitoringStatus.costMonitor.currentDailyCost.toFixed(4)}元`
  );
  console.log(
    `使用率: ${(monitoringStatus.costMonitor.usageRatio * 100).toFixed(1)}%`
  );

  // 6. 最终统计
  console.log("\n📊 系统统计信息:");
  const stats = aiServices.getStats();
  console.log(`总请求数: ${stats.totalRequests}`);
  console.log(`Qwen调用数: ${stats.qwenCalls}`);
  console.log(`DeepSeek调用数: ${stats.deepseekCalls}`);
  console.log(`缓存命中数: ${stats.cacheHits}`);

  console.log("\n✅ 系统功能测试完成！");

  // 清理测试缓存
  await dbManager.removeCandidateTypeCache("test-session-001");
  await dbManager.removeCandidateTypeCache("test-session-002");
  console.log("🧹 测试缓存已清理");
}

// 运行测试
if (require.main === module) {
  testSystem().catch(console.error);
}

module.exports = { testSystem };
