/**
 * 简单的HTTP测试服务器
 * 用于手动测试双模型架构
 */

const http = require("http");
const url = require("url");
const path = require("path");
const fs = require("fs");

// 手动加载环境变量
try {
  const envPath = path.join(__dirname, ".env");
  const envContent = fs.readFileSync(envPath, "utf8");

  envContent.split("\n").forEach((line) => {
    const [key, value] = line.split("=");
    if (key && value) {
      process.env[key.trim()] = value.trim();
    }
  });
} catch (error) {
  console.log("⚠️ 无法读取.env文件");
}

// 导入AI服务
const AIServices = require("./ai-recruitment-assistant/core/数据管理/ai-services");
const DatabaseManager = require("./ai-recruitment-assistant/core/数据管理/database-manager");
const ImprovedConversationManager = require("./improved-conversation-manager");

const PORT = 3000;

// 全局变量
let aiServices;
let database;
let conversationManager;
let sessions = new Map(); // 简单的会话存储

// 初始化服务
async function initializeServices() {
  try {
    console.log("🚀 正在初始化AI服务...");

    const config = {
      deepseekEndpoint: process.env.DEEPSEEK_ENDPOINT,
      deepseekApiKey: process.env.DEEPSEEK_API_KEY,
      qwenEndpoint: process.env.QWEN_ENDPOINT,
      qwenApiKey: process.env.QWEN_API_KEY,
      supabaseUrl: process.env.SUPABASE_URL,
      supabaseKey: process.env.SUPABASE_ANON_KEY,
      maxTokens: 2000,
      temperature: 0.7,
      timeout: 30000,
      dailyCostLimit: 50,
    };

    database = new DatabaseManager(config);
    aiServices = new AIServices(config);
    await aiServices.initialize();

    // 初始化改进的对话管理器
    conversationManager = new ImprovedConversationManager(aiServices, database);

    console.log("✅ 双模型AI服务初始化成功");
    console.log("   - DeepSeek V3: 主要回复生成");
    console.log("   - Qwen-Turbo: 候选人类型分析");
    console.log("✅ CALM-inspired对话管理器初始化成功");
    console.log("   - 实现 Thought1-Action-Observation-Thought2-Response 结构");
    console.log("   - 基于内容的智能缓存");
    console.log("   - 上下文感知的回复生成");
  } catch (error) {
    console.error("❌ 服务初始化失败:", error);
    throw error;
  }
}

// 处理HTTP请求
function handleRequest(req, res) {
  const parsedUrl = url.parse(req.url, true);
  const pathname = parsedUrl.pathname;
  const method = req.method;

  // 设置CORS头
  res.setHeader("Access-Control-Allow-Origin", "*");
  res.setHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
  res.setHeader("Access-Control-Allow-Headers", "Content-Type");

  if (method === "OPTIONS") {
    res.writeHead(200);
    res.end();
    return;
  }

  // 静态文件服务
  if (pathname === "/" || pathname === "/index.html") {
    try {
      const htmlContent = fs.readFileSync(
        path.join(__dirname, "public", "index.html"),
        "utf8"
      );
      res.writeHead(200, { "Content-Type": "text/html" });
      res.end(htmlContent);
    } catch (error) {
      res.writeHead(404);
      res.end("页面不存在");
    }
    return;
  }

  // API路由
  if (pathname === "/api/init" && method === "GET") {
    handleInit(req, res);
  } else if (pathname === "/api/chat" && method === "POST") {
    handleChat(req, res);
  } else if (pathname.startsWith("/api/stats/") && method === "GET") {
    const sessionId = pathname.split("/")[3];
    handleStats(req, res, sessionId);
  } else {
    res.writeHead(404);
    res.end("API不存在");
  }
}

// 处理初始化请求
function handleInit(req, res) {
  const sessionId = `session-${Date.now()}-${Math.random()
    .toString(36)
    .substring(2, 9)}`;

  // 存储会话
  sessions.set(sessionId, {
    id: sessionId,
    messages: [],
    createdAt: new Date(),
    candidateType: null,
  });

  const welcomeMessage = {
    type: "welcome",
    content: `您好，我是AI领域的猎头Katrina，专注于AI算法职位。
聊天框的左下角有简历上传的按钮，您可以分享最新的简历，便于我更精准的给您推荐职位。
同时，您也可以点击左下角的邮箱按钮，分享您的个人邮箱，我们可以更好的保持联系，定期的给您推送合适的职位。`,
    timestamp: new Date().toISOString(),
    sessionId: sessionId,
  };

  // 保存开场白到会话
  const session = sessions.get(sessionId);
  session.messages.push({
    role: "assistant",
    content: welcomeMessage.content,
    timestamp: new Date(),
  });

  console.log(`\n🎬 新会话开始: ${sessionId}`);
  console.log(`📱 发送开场白`);

  const response = {
    success: true,
    sessionId: sessionId,
    message: welcomeMessage,
  };

  res.writeHead(200, { "Content-Type": "application/json" });
  res.end(JSON.stringify(response));
}

// 处理聊天请求
function handleChat(req, res) {
  let body = "";

  req.on("data", (chunk) => {
    body += chunk.toString();
  });

  req.on("end", async () => {
    try {
      const { message, sessionId } = JSON.parse(body);

      if (!sessionId || !sessions.has(sessionId)) {
        res.writeHead(400, { "Content-Type": "application/json" });
        res.end(
          JSON.stringify({
            success: false,
            error: "无效的会话ID",
          })
        );
        return;
      }

      const session = sessions.get(sessionId);
      const timestamp = new Date();

      console.log(
        `\n[${timestamp.toLocaleTimeString()}] 👤 候选人: ${message}`
      );

      // 保存用户消息
      session.messages.push({
        role: "user",
        content: message,
        timestamp: timestamp,
      });

      // 构建对话历史
      const dialogue = session.messages.map((msg) => ({
        role: msg.role,
        content: msg.content,
      }));

      console.log(`🔍 CALM-inspired对话管理器开始工作...`);
      console.log(
        `   📊 当前对话轮次: ${Math.ceil(session.messages.length / 2)}`
      );

      const processingStart = Date.now();

      // 使用改进的对话管理器处理消息
      const result = await conversationManager.processMessage(
        sessionId,
        message,
        dialogue
      );
      const processingTime = Date.now() - processingStart;

      if (!result.success) {
        res.writeHead(500, { "Content-Type": "application/json" });
        res.end(
          JSON.stringify({
            success: false,
            error: result.error || "处理消息失败",
          })
        );
        return;
      }

      console.log(`✅ CALM处理完成 (耗时: ${processingTime}ms)`);

      // 更新会话中的AI回复
      session.candidateType = result.metadata.observation;

      // 保存AI回复到会话
      const aiReply = result.response.content;
      const aiStrategy = result.response.strategy;

      // 保存AI回复
      session.messages.push({
        role: "assistant",
        content: aiReply,
        timestamp: new Date(),
      });

      console.log(
        `[${new Date().toLocaleTimeString()}] 🤖 AI领域的猎头Katrina: ${aiReply}`
      );

      // 显示费用统计
      const stats = aiServices.getStats();
      const monitoring = aiServices.getMonitoringStatus();
      console.log(
        `💰 当前费用: ${monitoring.costMonitor.currentDailyCost.toFixed(6)}元`
      );
      console.log(
        `📞 API调用: Qwen=${stats.qwenCalls || 0}, DeepSeek=${
          stats.deepseekCalls || 0
        }, 缓存=${stats.cacheHits || 0}`
      );

      const response = {
        success: true,
        message: {
          type: "response",
          content: aiReply,
          timestamp: new Date().toISOString(),
          metadata: {
            candidateType: result.metadata.observation.type,
            confidence: result.metadata.observation.confidence,
            processingTime: processingTime,
            strategy: aiStrategy,
            thought1: result.metadata.thought1.reasoning,
            thought2: result.metadata.thought2.reasoning,
            action: result.metadata.action,
          },
        },
      };

      res.writeHead(200, { "Content-Type": "application/json" });
      res.end(JSON.stringify(response));
    } catch (error) {
      console.error(`❌ 处理消息失败:`, error);
      res.writeHead(500, { "Content-Type": "application/json" });
      res.end(
        JSON.stringify({
          success: false,
          error: "处理消息失败",
        })
      );
    }
  });
}

// 处理统计请求
function handleStats(req, res, sessionId) {
  if (!sessions.has(sessionId)) {
    res.writeHead(404, { "Content-Type": "application/json" });
    res.end(
      JSON.stringify({
        success: false,
        error: "会话不存在",
      })
    );
    return;
  }

  const session = sessions.get(sessionId);
  const stats = aiServices.getStats();
  const monitoring = aiServices.getMonitoringStatus();

  const response = {
    success: true,
    stats: {
      sessionId: sessionId,
      messageCount: session.messages.length,
      candidateType: session.candidateType,
      apiCalls: {
        qwen: stats.qwenCalls || 0,
        deepseek: stats.deepseekCalls || 0,
        cacheHits: stats.cacheHits || 0,
      },
      cost: monitoring.costMonitor.currentDailyCost,
      createdAt: session.createdAt,
    },
  };

  res.writeHead(200, { "Content-Type": "application/json" });
  res.end(JSON.stringify(response));
}

// 启动服务器
async function startServer() {
  try {
    await initializeServices();

    const server = http.createServer(handleRequest);

    server.listen(PORT, () => {
      console.log(`\n🌐 测试服务器启动成功！`);
      console.log(`📱 前端地址: http://localhost:${PORT}`);
      console.log(`🔗 API地址: http://localhost:${PORT}/api`);
      console.log(`\n📋 可用接口:`);
      console.log(`   GET  /api/init - 获取开场白`);
      console.log(`   POST /api/chat - 发送消息`);
      console.log(`   GET  /api/stats/:sessionId - 获取统计`);
      console.log(`\n🎯 现在您可以手动测试双模型架构了！`);
      console.log(`\n📊 我会在这里实时显示所有日志...`);
    });
  } catch (error) {
    console.error("❌ 服务器启动失败:", error);
    process.exit(1);
  }
}

startServer();
