/**
 * 简单的HTTP测试服务器
 * 用于手动测试双模型架构
 */

const http = require("http");
const url = require("url");
const path = require("path");
const fs = require("fs");

// 手动加载环境变量
try {
  const envPath = path.join(__dirname, ".env");
  const envContent = fs.readFileSync(envPath, "utf8");

  envContent.split("\n").forEach((line) => {
    const [key, value] = line.split("=");
    if (key && value) {
      process.env[key.trim()] = value.trim();
    }
  });
} catch (error) {
  console.log("⚠️ 无法读取.env文件");
}

// 导入AI服务
const AIServices = require("./ai-recruitment-assistant/core/数据管理/ai-services");
const DatabaseManager = require("./ai-recruitment-assistant/core/数据管理/database-manager");

const PORT = 3000;

// 全局变量
let aiServices;
let database;
let sessions = new Map(); // 简单的会话存储

// 初始化服务
async function initializeServices() {
  try {
    console.log("🚀 正在初始化AI服务...");

    const config = {
      deepseekEndpoint: process.env.DEEPSEEK_ENDPOINT,
      deepseekApiKey: process.env.DEEPSEEK_API_KEY,
      qwenEndpoint: process.env.QWEN_ENDPOINT,
      qwenApiKey: process.env.QWEN_API_KEY,
      supabaseUrl: process.env.SUPABASE_URL,
      supabaseKey: process.env.SUPABASE_ANON_KEY,
      maxTokens: 2000,
      temperature: 0.7,
      timeout: 30000,
      dailyCostLimit: 50,
    };

    database = new DatabaseManager(config);
    aiServices = new AIServices(config);
    await aiServices.initialize();

    console.log("✅ 双模型AI服务初始化成功");
    console.log("   - DeepSeek V3: 主要回复生成");
    console.log("   - Qwen-Turbo: 候选人类型分析");
  } catch (error) {
    console.error("❌ 服务初始化失败:", error);
    throw error;
  }
}

// 处理HTTP请求
function handleRequest(req, res) {
  const parsedUrl = url.parse(req.url, true);
  const pathname = parsedUrl.pathname;
  const method = req.method;

  // 设置CORS头
  res.setHeader("Access-Control-Allow-Origin", "*");
  res.setHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
  res.setHeader("Access-Control-Allow-Headers", "Content-Type");

  if (method === "OPTIONS") {
    res.writeHead(200);
    res.end();
    return;
  }

  // 静态文件服务
  if (pathname === "/" || pathname === "/index.html") {
    try {
      const htmlContent = fs.readFileSync(
        path.join(__dirname, "public", "index.html"),
        "utf8"
      );
      res.writeHead(200, { "Content-Type": "text/html" });
      res.end(htmlContent);
    } catch (error) {
      res.writeHead(404);
      res.end("页面不存在");
    }
    return;
  }

  // API路由
  if (pathname === "/api/init" && method === "GET") {
    handleInit(req, res);
  } else if (pathname === "/api/chat" && method === "POST") {
    handleChat(req, res);
  } else if (pathname.startsWith("/api/stats/") && method === "GET") {
    const sessionId = pathname.split("/")[3];
    handleStats(req, res, sessionId);
  } else {
    res.writeHead(404);
    res.end("API不存在");
  }
}

// 处理初始化请求
function handleInit(req, res) {
  const sessionId = `session-${Date.now()}-${Math.random()
    .toString(36)
    .substring(2, 9)}`;

  // 存储会话
  sessions.set(sessionId, {
    id: sessionId,
    messages: [],
    createdAt: new Date(),
    candidateType: null,
  });

  const welcomeMessage = {
    type: "welcome",
    content: `您好，我是AI领域的猎头Katrina，专注于AI算法职位。
聊天框的左下角有简历上传的按钮，您可以分享最新的简历，便于我更精准的给您推荐职位。
同时，您也可以点击左下角的邮箱按钮，分享您的个人邮箱，我们可以更好的保持联系，定期的给您推送合适的职位。`,
    timestamp: new Date().toISOString(),
    sessionId: sessionId,
  };

  // 保存开场白到会话
  const session = sessions.get(sessionId);
  session.messages.push({
    role: "assistant",
    content: welcomeMessage.content,
    timestamp: new Date(),
  });

  console.log(`\n🎬 新会话开始: ${sessionId}`);
  console.log(`📱 发送开场白`);

  const response = {
    success: true,
    sessionId: sessionId,
    message: welcomeMessage,
  };

  res.writeHead(200, { "Content-Type": "application/json" });
  res.end(JSON.stringify(response));
}

// 处理聊天请求
function handleChat(req, res) {
  let body = "";

  req.on("data", (chunk) => {
    body += chunk.toString();
  });

  req.on("end", async () => {
    try {
      const { message, sessionId } = JSON.parse(body);

      if (!sessionId || !sessions.has(sessionId)) {
        res.writeHead(400, { "Content-Type": "application/json" });
        res.end(
          JSON.stringify({
            success: false,
            error: "无效的会话ID",
          })
        );
        return;
      }

      const session = sessions.get(sessionId);
      const timestamp = new Date();

      console.log(
        `\n[${timestamp.toLocaleTimeString()}] 👤 候选人: ${message}`
      );

      // 保存用户消息
      session.messages.push({
        role: "user",
        content: message,
        timestamp: timestamp,
      });

      // 构建对话历史
      const dialogue = session.messages.map((msg) => ({
        role: msg.role,
        content: msg.content,
      }));

      console.log(`🔍 双模型系统开始工作...`);
      console.log(
        `   📊 当前对话轮次: ${Math.ceil(session.messages.length / 2)}`
      );
      console.log(`   🧠 Qwen-Turbo: 分析候选人类型`);

      const analysisStart = Date.now();

      // 调用候选人类型分析
      const candidateAnalysis = await aiServices.analyzeCandidateType(
        dialogue,
        sessionId
      );
      const analysisTime = Date.now() - analysisStart;

      console.log(`✅ 候选人类型分析完成 (耗时: ${analysisTime}ms)`);
      console.log(`   📊 类型: ${candidateAnalysis.type}`);
      console.log(
        `   📊 置信度: ${(candidateAnalysis.confidence * 100).toFixed(1)}%`
      );
      console.log(
        `   📊 主要证据: ${candidateAnalysis.evidence.slice(0, 2).join("; ")}`
      );
      console.log(`   📊 数据源: ${candidateAnalysis.source || "AI分析"}`);

      if (analysisTime < 100) {
        console.log(`   🎯 缓存命中！显著提升响应速度`);
      }

      // 更新会话中的候选人类型
      session.candidateType = candidateAnalysis;

      // 根据您的真实业务逻辑生成回复
      let aiReply = "";
      const userMessage = message.toLowerCase().trim();

      console.log(`   💬 根据业务逻辑生成回复...`);

      // 按照您的真实逻辑判断回复类型
      const userRoundCount = session.messages.filter(
        (m) => m.role === "user"
      ).length;

      if (userRoundCount === 1) {
        // 第一句用户回复
        if (
          userMessage.includes("你好") ||
          userMessage.includes("hi") ||
          userMessage.includes("hello")
        ) {
          aiReply = "您考虑看看新机会吗？优质的职位还挺多的。";
          console.log(`   📝 回复策略: 问候语硬编码回复`);
        } else if (
          userMessage.includes("职位") ||
          userMessage.includes("工作") ||
          userMessage.includes("有什么")
        ) {
          aiReply =
            "我们当前合作了很多公司，包含头部大厂、中型公司、创业型团队、国央企等等，职位也比较多，麻烦您告知一下您的信息点，便于我能够给您来推荐合适的职位。";
          console.log(`   📝 回复策略: 职位询问硬编码回复`);
        } else if (candidateAnalysis.type === "挤牙膏型") {
          aiReply = "您考虑看看新机会吗？优质的职位还挺多的。";
          console.log(`   📝 回复策略: 挤牙膏型引导`);
        } else {
          aiReply = "好的，请问您目前在考虑什么样的工作机会呢？";
          console.log(`   📝 回复策略: 通用引导`);
        }
      } else if (userRoundCount === 2) {
        // 第二句回复
        if (candidateAnalysis.type === "挤牙膏型") {
          aiReply =
            "我们当前合作了很多公司，包含头部大厂、中型公司、创业型团队、国央企等等，职位也比较多，麻烦您告知一下您的信息点，便于我能够给您来推荐合适的职位。";
          console.log(`   📝 回复策略: 挤牙膏型 - 引导提供信息`);
        } else if (
          userMessage.includes("职位") ||
          userMessage.includes("工作") ||
          userMessage.includes("有什么")
        ) {
          aiReply =
            "我们当前合作了很多公司，包含头部大厂、中型公司、创业型团队、国央企等等，职位也比较多，麻烦您告知一下您的信息点，便于我能够给您来推荐合适的职位。";
          console.log(`   📝 回复策略: 职位询问硬编码回复`);
        } else {
          aiReply =
            "得拜托您告知我，您的技术栈或技术方向、当前所在公司、当前的职级、期望的薪资？";
          console.log(`   📝 回复策略: 信息收集`);
        }
      } else {
        // 后续对话
        if (
          userMessage.includes("算法") ||
          userMessage.includes("机器学习") ||
          userMessage.includes("AI")
        ) {
          aiReply = `很好！${message}方向确实有很多优质机会。请问您目前的工作经验和期望薪资是多少呢？`;
          console.log(`   📝 回复策略: 技术方向确认`);
        } else if (
          userMessage.includes("腾讯") ||
          userMessage.includes("阿里") ||
          userMessage.includes("字节")
        ) {
          aiReply = `了解，您在${
            userMessage.includes("腾讯")
              ? "腾讯"
              : userMessage.includes("阿里")
              ? "阿里"
              : "字节"
          }的背景很不错。基于您的经验，我这边有几个不错的职位推荐。您对哪个技术方向更感兴趣呢？`;
          console.log(`   📝 回复策略: 大厂背景确认`);
        } else if (
          userMessage.includes("需要") ||
          userMessage.includes("什么信息")
        ) {
          aiReply =
            "得拜托您告知我，您的技术栈或技术方向、当前所在公司、当前的职级、期望的薪资？";
          console.log(`   📝 回复策略: 明确信息需求`);
        } else {
          aiReply =
            "好的，我了解了。还有其他信息可以分享吗？这样我能为您推荐更合适的职位。";
          console.log(`   📝 回复策略: 通用确认`);
        }
      }

      // 保存AI回复
      session.messages.push({
        role: "assistant",
        content: aiReply,
        timestamp: new Date(),
      });

      console.log(
        `[${new Date().toLocaleTimeString()}] 🤖 AI领域的猎头Katrina: ${aiReply}`
      );

      // 显示费用统计
      const stats = aiServices.getStats();
      const monitoring = aiServices.getMonitoringStatus();
      console.log(
        `💰 当前费用: ${monitoring.costMonitor.currentDailyCost.toFixed(6)}元`
      );
      console.log(
        `📞 API调用: Qwen=${stats.qwenCalls || 0}, DeepSeek=${
          stats.deepseekCalls || 0
        }, 缓存=${stats.cacheHits || 0}`
      );

      const response = {
        success: true,
        message: {
          type: "response",
          content: aiReply,
          timestamp: new Date().toISOString(),
          metadata: {
            candidateType: candidateAnalysis.type,
            confidence: candidateAnalysis.confidence,
            analysisTime: analysisTime,
            source: candidateAnalysis.source || "AI分析",
          },
        },
      };

      res.writeHead(200, { "Content-Type": "application/json" });
      res.end(JSON.stringify(response));
    } catch (error) {
      console.error(`❌ 处理消息失败:`, error);
      res.writeHead(500, { "Content-Type": "application/json" });
      res.end(
        JSON.stringify({
          success: false,
          error: "处理消息失败",
        })
      );
    }
  });
}

// 处理统计请求
function handleStats(req, res, sessionId) {
  if (!sessions.has(sessionId)) {
    res.writeHead(404, { "Content-Type": "application/json" });
    res.end(
      JSON.stringify({
        success: false,
        error: "会话不存在",
      })
    );
    return;
  }

  const session = sessions.get(sessionId);
  const stats = aiServices.getStats();
  const monitoring = aiServices.getMonitoringStatus();

  const response = {
    success: true,
    stats: {
      sessionId: sessionId,
      messageCount: session.messages.length,
      candidateType: session.candidateType,
      apiCalls: {
        qwen: stats.qwenCalls || 0,
        deepseek: stats.deepseekCalls || 0,
        cacheHits: stats.cacheHits || 0,
      },
      cost: monitoring.costMonitor.currentDailyCost,
      createdAt: session.createdAt,
    },
  };

  res.writeHead(200, { "Content-Type": "application/json" });
  res.end(JSON.stringify(response));
}

// 启动服务器
async function startServer() {
  try {
    await initializeServices();

    const server = http.createServer(handleRequest);

    server.listen(PORT, () => {
      console.log(`\n🌐 测试服务器启动成功！`);
      console.log(`📱 前端地址: http://localhost:${PORT}`);
      console.log(`🔗 API地址: http://localhost:${PORT}/api`);
      console.log(`\n📋 可用接口:`);
      console.log(`   GET  /api/init - 获取开场白`);
      console.log(`   POST /api/chat - 发送消息`);
      console.log(`   GET  /api/stats/:sessionId - 获取统计`);
      console.log(`\n🎯 现在您可以手动测试双模型架构了！`);
      console.log(`\n📊 我会在这里实时显示所有日志...`);
    });
  } catch (error) {
    console.error("❌ 服务器启动失败:", error);
    process.exit(1);
  }
}

startServer();
