/**
 * 真实API调用测试
 * 测试不同类型候选人的对话场景
 */

// 手动加载环境变量
const fs = require("fs");
const path = require("path");

// 读取.env文件
try {
  const envPath = path.join(__dirname, ".env");
  const envContent = fs.readFileSync(envPath, "utf8");

  envContent.split("\n").forEach((line) => {
    const [key, value] = line.split("=");
    if (key && value) {
      process.env[key.trim()] = value.trim();
    }
  });
} catch (error) {
  console.log("⚠️ 无法读取.env文件，使用默认配置");
}

const AIServices = require("./ai-recruitment-assistant/core/数据管理/ai-services");
const DatabaseManager = require("./ai-recruitment-assistant/core/数据管理/database-manager");

// 真实配置
const realConfig = {
  deepseekEndpoint: process.env.DEEPSEEK_ENDPOINT,
  deepseekApiKey: process.env.DEEPSEEK_API_KEY,
  qwenEndpoint: process.env.QWEN_ENDPOINT,
  qwenApiKey: process.env.QWEN_API_KEY,
  supabaseUrl: process.env.SUPABASE_URL,
  supabaseKey: process.env.SUPABASE_ANON_KEY,
  maxTokens: 2000,
  temperature: 0.7,
  timeout: 30000,
  dailyCostLimit: 50,
};

// 不同类型的候选人对话场景
const candidateScenarios = {
  正常型候选人: [
    {
      role: "assistant",
      content: "您好，我是AI招聘助手Katrina，很高兴为您服务！",
    },
    { role: "user", content: "你好，我想了解一下贵公司的职位" },
    { role: "assistant", content: "好的，请问您的技术背景是什么呢？" },
    { role: "user", content: "我是做Java开发的，有5年经验，主要做后端开发" },
    { role: "assistant", content: "很好！请问您期望的薪资范围是多少？" },
    { role: "user", content: "希望能在25-30k之间，看具体的职位和公司" },
  ],

  挤牙膏型候选人: [
    {
      role: "assistant",
      content: "您好，我是AI招聘助手Katrina，很高兴为您服务！",
    },
    { role: "user", content: "嗯" },
    { role: "assistant", content: "请问您在找什么类型的工作呢？" },
    { role: "user", content: "看看" },
    { role: "assistant", content: "好的，请问您的技术背景是什么？" },
    { role: "user", content: "开发" },
    {
      role: "assistant",
      content: "具体是什么技术栈呢？比如Java、Python还是前端？",
    },
    { role: "user", content: "Java" },
    { role: "assistant", content: "有几年经验了？" },
    { role: "user", content: "3年" },
  ],

  疯狂咨询型候选人: [
    {
      role: "assistant",
      content: "您好，我是AI招聘助手Katrina，很高兴为您服务！",
    },
    {
      role: "user",
      content: "你们公司有什么职位？薪资怎么样？福利如何？加班多吗？",
    },
    {
      role: "assistant",
      content: "我们有很多技术职位，请问您的技术背景是什么呢？",
    },
    {
      role: "user",
      content: "你先说说都有什么职位？要求是什么？薪资范围多少？",
    },
    {
      role: "assistant",
      content: "为了给您推荐最合适的职位，能先了解一下您的技术栈吗？",
    },
    { role: "user", content: "你们公司规模多大？在哪个城市？发展前景怎么样？" },
    {
      role: "assistant",
      content: "我们是一家成长型公司，请问您方便介绍一下自己的背景吗？",
    },
    { role: "user", content: "你们的面试流程是什么？多久能拿到offer？" },
  ],

  质疑不信任型候选人: [
    {
      role: "assistant",
      content: "您好，我是AI招聘助手Katrina，很高兴为您服务！",
    },
    { role: "user", content: "你真的是AI吗？不会是假的吧？" },
    { role: "assistant", content: "是的，我是AI助手，可以帮您了解职位信息" },
    { role: "user", content: "你们公司靠谱吗？不会是骗子公司吧？" },
    {
      role: "assistant",
      content: "我们是正规公司，您可以查看我们的官网和资质",
    },
    { role: "user", content: "听说很多AI招聘都是套路，你们不会也是吧？" },
    { role: "assistant", content: "我们致力于为候选人提供真实的职位信息" },
    { role: "user", content: "真的有这么好的职位吗？我怀疑是不是有什么陷阱" },
  ],
};

async function testRealAPI() {
  console.log("🚀 开始真实API调用测试...\n");
  console.log("=" * 60);

  // 初始化服务
  const aiServices = new AIServices(realConfig);
  const dbManager = new DatabaseManager(realConfig);

  try {
    await aiServices.initialize();
    console.log("✅ AI服务初始化成功\n");
  } catch (error) {
    console.error("❌ AI服务初始化失败:", error);
    return;
  }

  // 测试每种候选人类型
  for (const [candidateType, dialogue] of Object.entries(candidateScenarios)) {
    console.log(`\n${"=".repeat(50)}`);
    console.log(`🎭 测试场景: ${candidateType}`);
    console.log(`${"=".repeat(50)}`);

    const sessionId = `test-${candidateType.replace(
      /[^a-zA-Z0-9]/g,
      "-"
    )}-${Date.now()}`;

    try {
      // 显示对话历史
      console.log("\n📝 对话历史:");
      dialogue.forEach((msg, index) => {
        const speaker = msg.role === "assistant" ? "🤖 AI" : "👤 候选人";
        console.log(`${index + 1}. ${speaker}: ${msg.content}`);
      });

      console.log("\n🔍 开始AI分析...");
      const startTime = Date.now();

      // 调用真实API进行分析
      const analysis = await aiServices.analyzeCandidateType(
        dialogue,
        sessionId
      );

      const endTime = Date.now();
      const duration = endTime - startTime;

      // 显示分析结果
      console.log("\n📊 分析结果:");
      console.log(`类型: ${analysis.type}`);
      console.log(`置信度: ${(analysis.confidence * 100).toFixed(1)}%`);
      console.log(`证据: ${analysis.evidence.join(", ")}`);
      console.log(`策略: ${analysis.strategy}`);
      console.log(`风险等级: ${analysis.riskLevel}`);
      console.log(`数据源: ${analysis.source || "AI分析"}`);
      console.log(`分析耗时: ${duration}ms`);

      // 测试缓存命中
      console.log("\n🔄 测试缓存命中...");
      const cacheStartTime = Date.now();
      const cachedAnalysis = await aiServices.analyzeCandidateType(
        dialogue,
        sessionId
      );
      const cacheEndTime = Date.now();
      const cacheDuration = cacheEndTime - cacheStartTime;

      if (cachedAnalysis === analysis) {
        console.log(`✅ 缓存命中 (耗时: ${cacheDuration}ms)`);
      } else {
        console.log(`❌ 缓存未命中`);
      }

      // 显示费用信息
      const stats = aiServices.getStats();
      const monitoring = aiServices.getMonitoringStatus();
      console.log("\n💰 费用统计:");
      console.log(
        `当前费用: ${monitoring.costMonitor.currentDailyCost.toFixed(4)}元`
      );
      console.log(
        `API调用次数: Qwen=${stats.qwenCalls || 0}, DeepSeek=${
          stats.deepseekCalls || 0
        }`
      );
      console.log(`缓存命中: ${stats.cacheHits || 0}次`);
    } catch (error) {
      console.error(`❌ ${candidateType} 测试失败:`, error.message);
    }

    // 等待一下，避免API限流
    await new Promise((resolve) => setTimeout(resolve, 2000));
  }

  // 最终统计
  console.log("\n" + "=".repeat(60));
  console.log("📈 最终统计信息");
  console.log("=".repeat(60));

  const finalStats = aiServices.getStats();
  const finalMonitoring = aiServices.getMonitoringStatus();

  console.log(
    `总API调用: ${
      (finalStats.qwenCalls || 0) + (finalStats.deepseekCalls || 0)
    }次`
  );
  console.log(`Qwen调用: ${finalStats.qwenCalls || 0}次`);
  console.log(`DeepSeek调用: ${finalStats.deepseekCalls || 0}次`);
  console.log(`缓存命中: ${finalStats.cacheHits || 0}次`);
  console.log(
    `总费用: ${finalMonitoring.costMonitor.currentDailyCost.toFixed(4)}元`
  );

  const totalCalls =
    (finalStats.qwenCalls || 0) + (finalStats.deepseekCalls || 0);
  const cacheHitRate =
    totalCalls > 0
      ? (finalStats.cacheHits || 0) / (totalCalls + (finalStats.cacheHits || 0))
      : 0;
  console.log(`缓存命中率: ${(cacheHitRate * 100).toFixed(1)}%`);

  // 清理测试缓存
  console.log("\n🧹 清理测试缓存...");
  for (const candidateType of Object.keys(candidateScenarios)) {
    const sessionId = `test-${candidateType.replace(/[^a-zA-Z0-9]/g, "-")}`;
    try {
      await dbManager.removeCandidateTypeCache(sessionId);
    } catch (error) {
      // 忽略清理错误
    }
  }

  console.log("\n✅ 真实API测试完成！");
}

// 运行测试
if (require.main === module) {
  testRealAPI().catch(console.error);
}

module.exports = { testRealAPI };
