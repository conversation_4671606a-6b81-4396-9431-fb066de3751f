# AI招聘助手系统环境变量配置
# 复制此文件为 .env 并填入实际的API密钥

# ==================== 数据库配置 ====================
# Supabase 数据库配置
SUPABASE_URL=your_supabase_url_here
SUPABASE_ANON_KEY=your_supabase_anon_key_here

# ==================== AI模型配置 ====================

# DeepSeek V3 配置（主要回复生成）
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_ENDPOINT=https://api.deepseek.com/v1/chat/completions
DEEPSEEK_MODEL=deepseek-chat

# Qwen-Turbo 配置（候选人类型分析）
QWEN_API_KEY=your_qwen_api_key_here
QWEN_ENDPOINT=https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation
QWEN_MODEL=qwen-turbo

# ==================== 系统配置 ====================

# 服务器配置
PORT=3000
NODE_ENV=development

# 费用控制
DAILY_COST_LIMIT=50
COST_ALERT_THRESHOLD=0.8

# 缓存配置
CACHE_DIRECTORY=./cache/candidate_types
MAX_CACHE_SIZE=50000

# ==================== 安全配置 ====================

# JWT密钥（用于用户认证）
JWT_SECRET=your_jwt_secret_here

# API限流配置
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# ==================== 日志配置 ====================

# 日志级别：error, warn, info, debug
LOG_LEVEL=info
LOG_FILE_PATH=./logs/app.log

# ==================== 通知配置 ====================

# 钉钉机器人（用于系统告警）
DINGTALK_WEBHOOK_URL=your_dingtalk_webhook_url_here

# 邮件通知配置
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password_here
ADMIN_EMAIL=<EMAIL>
