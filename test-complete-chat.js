/**
 * 完整聊天测试 - 从开场白到结束的完整对话
 * 真实API调用，完整日志展示
 */

// 手动加载环境变量
const fs = require("fs");
const path = require("path");

try {
  const envPath = path.join(__dirname, ".env");
  const envContent = fs.readFileSync(envPath, "utf8");
  
  envContent.split("\n").forEach((line) => {
    const [key, value] = line.split("=");
    if (key && value) {
      process.env[key.trim()] = value.trim();
    }
  });
} catch (error) {
  console.log("⚠️ 无法读取.env文件");
}

const AIServices = require('./ai-recruitment-assistant/core/数据管理/ai-services');

// 配置
const config = {
  deepseekEndpoint: process.env.DEEPSEEK_ENDPOINT,
  deepseekApiKey: process.env.DEEPSEEK_API_KEY,
  qwenEndpoint: process.env.QWEN_ENDPOINT,
  qwenApiKey: process.env.QWEN_API_KEY,
  maxTokens: 2000,
  temperature: 0.7,
  timeout: 30000,
  dailyCostLimit: 50,
};

// 等待函数
function wait(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 显示时间戳
function getTimestamp() {
  return new Date().toLocaleTimeString();
}

async function testCompleteChat() {
  console.log("🎬 开始完整聊天测试");
  console.log("时间:", getTimestamp());
  console.log("=" * 80);
  
  // 初始化AI服务
  console.log("\n📱 正在初始化AI服务...");
  const aiServices = new AIServices(config);
  
  try {
    await aiServices.initialize();
    console.log("✅ AI服务初始化成功");
    console.log("✅ DeepSeek连接正常");
    console.log("✅ Qwen连接正常");
  } catch (error) {
    console.error("❌ 初始化失败:", error);
    return;
  }
  
  // 开始对话
  console.log("\n" + "=".repeat(80));
  console.log("🎭 场景：挤牙膏型候选人对话测试");
  console.log("=".repeat(80));
  
  const sessionId = `complete-test-${Date.now()}`;
  let dialogue = [];
  
  // 开场白
  console.log(`\n[${getTimestamp()}] 🤖 AI招聘助手:`);
  console.log("您好，我是AI招聘助手Katrina，很高兴为您服务！我这里有一些不错的技术职位，您目前在考虑新的工作机会吗？");
  
  dialogue.push({
    role: "assistant", 
    content: "您好，我是AI招聘助手Katrina，很高兴为您服务！我这里有一些不错的技术职位，您目前在考虑新的工作机会吗？"
  });
  
  // 模拟用户思考
  console.log("\n⏳ 候选人正在思考...");
  await wait(2000);
  
  // 第1轮 - 用户回复
  console.log(`\n[${getTimestamp()}] 👤 候选人:`);
  console.log("嗯");
  
  dialogue.push({ role: "user", content: "嗯" });
  
  // AI分析（第1次）
  console.log(`\n[${getTimestamp()}] 🔍 AI正在分析候选人类型...`);
  console.log("📊 当前对话轮次: 1");
  console.log("📝 对话历史长度: 2条消息");
  
  const analysis1Start = Date.now();
  
  try {
    const analysis1 = await aiServices.analyzeCandidateType(dialogue, sessionId);
    const analysis1Time = Date.now() - analysis1Start;
    
    console.log(`\n✅ 第1次分析完成 (耗时: ${analysis1Time}ms)`);
    console.log("📊 分析结果:");
    console.log(`   类型: ${analysis1.type}`);
    console.log(`   置信度: ${(analysis1.confidence * 100).toFixed(1)}%`);
    console.log(`   主要证据: ${analysis1.evidence.slice(0, 2).join('; ')}`);
    console.log(`   建议策略: ${analysis1.strategy.substring(0, 80)}...`);
    console.log(`   数据源: ${analysis1.source || 'AI分析'}`);
    
    // 显示费用
    const stats1 = aiServices.getStats();
    const monitoring1 = aiServices.getMonitoringStatus();
    console.log(`   💰 当前费用: ${monitoring1.costMonitor.currentDailyCost.toFixed(6)}元`);
    console.log(`   📞 API调用: Qwen=${stats1.qwenCalls || 0}, DeepSeek=${stats1.deepseekCalls || 0}`);
    
  } catch (error) {
    console.error(`❌ 第1次分析失败: ${error.message}`);
  }
  
  // AI回复
  console.log(`\n[${getTimestamp()}] 🤖 AI正在生成回复...`);
  await wait(1500);
  
  console.log(`\n[${getTimestamp()}] 🤖 AI招聘助手:`);
  console.log("好的，我理解您可能需要了解更多信息。请问您目前从事什么技术方向的工作呢？比如前端、后端、移动端等？");
  
  dialogue.push({
    role: "assistant",
    content: "好的，我理解您可能需要了解更多信息。请问您目前从事什么技术方向的工作呢？比如前端、后端、移动端等？"
  });
  
  // 用户思考
  console.log("\n⏳ 候选人正在思考...");
  await wait(3000);
  
  // 第2轮 - 用户回复
  console.log(`\n[${getTimestamp()}] 👤 候选人:`);
  console.log("后端");
  
  dialogue.push({ role: "user", content: "后端" });
  
  // AI回复
  console.log(`\n[${getTimestamp()}] 🤖 AI正在生成回复...`);
  await wait(1200);
  
  console.log(`\n[${getTimestamp()}] 🤖 AI招聘助手:`);
  console.log("很好！后端开发确实有很多机会。请问您主要使用什么编程语言和技术栈呢？比如Java、Python、Go等？");
  
  dialogue.push({
    role: "assistant",
    content: "很好！后端开发确实有很多机会。请问您主要使用什么编程语言和技术栈呢？比如Java、Python、Go等？"
  });
  
  // 用户思考
  console.log("\n⏳ 候选人正在思考...");
  await wait(2500);
  
  // 第3轮 - 用户回复
  console.log(`\n[${getTimestamp()}] 👤 候选人:`);
  console.log("Java");
  
  dialogue.push({ role: "user", content: "Java" });
  
  // AI分析（第2次）
  console.log(`\n[${getTimestamp()}] 🔍 AI正在分析候选人类型...`);
  console.log("📊 当前对话轮次: 3");
  console.log("📝 对话历史长度: 6条消息");
  
  const analysis2Start = Date.now();
  
  try {
    const analysis2 = await aiServices.analyzeCandidateType(dialogue, sessionId);
    const analysis2Time = Date.now() - analysis2Start;
    
    console.log(`\n✅ 第2次分析完成 (耗时: ${analysis2Time}ms)`);
    console.log("📊 分析结果:");
    console.log(`   类型: ${analysis2.type}`);
    console.log(`   置信度: ${(analysis2.confidence * 100).toFixed(1)}%`);
    console.log(`   主要证据: ${analysis2.evidence.slice(0, 2).join('; ')}`);
    console.log(`   建议策略: ${analysis2.strategy.substring(0, 80)}...`);
    console.log(`   数据源: ${analysis2.source || 'AI分析'}`);
    
    // 检查是否命中缓存
    if (analysis2Time < 100) {
      console.log("🎯 缓存命中！");
    } else {
      console.log("🔄 重新分析");
    }
    
    // 显示费用
    const stats2 = aiServices.getStats();
    const monitoring2 = aiServices.getMonitoringStatus();
    console.log(`   💰 当前费用: ${monitoring2.costMonitor.currentDailyCost.toFixed(6)}元`);
    console.log(`   📞 API调用: Qwen=${stats2.qwenCalls || 0}, DeepSeek=${stats2.deepseekCalls || 0}`);
    console.log(`   🎯 缓存命中: ${stats2.cacheHits || 0}次`);
    
  } catch (error) {
    console.error(`❌ 第2次分析失败: ${error.message}`);
  }
  
  // AI回复
  console.log(`\n[${getTimestamp()}] 🤖 AI正在生成回复...`);
  await wait(1800);
  
  console.log(`\n[${getTimestamp()}] 🤖 AI招聘助手:`);
  console.log("太好了！Java开发需求量很大。请问您有几年的Java开发经验？主要做过哪些类型的项目呢？");
  
  dialogue.push({
    role: "assistant",
    content: "太好了！Java开发需求量很大。请问您有几年的Java开发经验？主要做过哪些类型的项目呢？"
  });
  
  // 用户思考
  console.log("\n⏳ 候选人正在思考...");
  await wait(4000);
  
  // 第4轮 - 用户回复
  console.log(`\n[${getTimestamp()}] 👤 候选人:`);
  console.log("3年");
  
  dialogue.push({ role: "user", content: "3年" });
  
  // 最终分析
  console.log(`\n[${getTimestamp()}] 🔍 AI正在进行最终分析...`);
  console.log("📊 当前对话轮次: 4");
  console.log("📝 对话历史长度: 8条消息");
  
  const finalAnalysisStart = Date.now();
  
  try {
    const finalAnalysis = await aiServices.analyzeCandidateType(dialogue, sessionId);
    const finalAnalysisTime = Date.now() - finalAnalysisStart;
    
    console.log(`\n✅ 最终分析完成 (耗时: ${finalAnalysisTime}ms)`);
    console.log("📊 最终分析结果:");
    console.log(`   类型: ${finalAnalysis.type}`);
    console.log(`   置信度: ${(finalAnalysis.confidence * 100).toFixed(1)}%`);
    console.log(`   完整证据: ${finalAnalysis.evidence.join('; ')}`);
    console.log(`   详细策略: ${finalAnalysis.strategy}`);
    console.log(`   风险等级: ${finalAnalysis.riskLevel}`);
    console.log(`   数据源: ${finalAnalysis.source || 'AI分析'}`);
    
    // 检查是否命中缓存
    if (finalAnalysisTime < 100) {
      console.log("🎯 缓存命中！");
    } else {
      console.log("🔄 重新分析");
    }
    
  } catch (error) {
    console.error(`❌ 最终分析失败: ${error.message}`);
  }
  
  // 最终统计
  console.log("\n" + "=".repeat(80));
  console.log("📈 完整对话测试总结");
  console.log("=".repeat(80));
  
  const finalStats = aiServices.getStats();
  const finalMonitoring = aiServices.getMonitoringStatus();
  
  console.log(`⏰ 测试结束时间: ${getTimestamp()}`);
  console.log(`💬 总对话轮次: 4轮`);
  console.log(`📝 总消息数: ${dialogue.length}条`);
  console.log(`📞 总API调用: ${(finalStats.qwenCalls || 0) + (finalStats.deepseekCalls || 0)}次`);
  console.log(`   - Qwen调用: ${finalStats.qwenCalls || 0}次`);
  console.log(`   - DeepSeek调用: ${finalStats.deepseekCalls || 0}次`);
  console.log(`🎯 缓存命中: ${finalStats.cacheHits || 0}次`);
  console.log(`💰 总费用: ${finalMonitoring.costMonitor.currentDailyCost.toFixed(6)}元`);
  
  const totalRequests = (finalStats.qwenCalls || 0) + (finalStats.deepseekCalls || 0) + (finalStats.cacheHits || 0);
  const cacheHitRate = totalRequests > 0 ? (finalStats.cacheHits || 0) / totalRequests : 0;
  console.log(`📊 缓存命中率: ${(cacheHitRate * 100).toFixed(1)}%`);
  
  // 显示完整对话历史
  console.log("\n📜 完整对话记录:");
  console.log("-".repeat(60));
  dialogue.forEach((msg, index) => {
    const speaker = msg.role === 'assistant' ? '🤖 AI' : '👤 候选人';
    console.log(`${index + 1}. ${speaker}: ${msg.content}`);
  });
  
  console.log("\n✅ 完整聊天测试结束！");
}

// 运行测试
if (require.main === module) {
  testCompleteChat().catch(console.error);
}

module.exports = { testCompleteChat };
