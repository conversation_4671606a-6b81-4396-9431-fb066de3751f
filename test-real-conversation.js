/**
 * 真实对话测试 - 逐句模拟真人对话
 * 测试真实的API调用时间和候选人类型分析
 */

// 手动加载环境变量
const fs = require("fs");
const path = require("path");

try {
  const envPath = path.join(__dirname, ".env");
  const envContent = fs.readFileSync(envPath, "utf8");
  
  envContent.split("\n").forEach((line) => {
    const [key, value] = line.split("=");
    if (key && value) {
      process.env[key.trim()] = value.trim();
    }
  });
} catch (error) {
  console.log("⚠️ 无法读取.env文件");
}

const AIServices = require('./ai-recruitment-assistant/core/数据管理/ai-services');

// 真实配置
const config = {
  deepseekEndpoint: process.env.DEEPSEEK_ENDPOINT,
  deepseekApiKey: process.env.DEEPSEEK_API_KEY,
  qwenEndpoint: process.env.QWEN_ENDPOINT,
  qwenApiKey: process.env.QWEN_API_KEY,
  maxTokens: 2000,
  temperature: 0.7,
  timeout: 30000,
  dailyCostLimit: 50,
};

// 真实候选人对话场景
const realConversations = {
  "正常配合型": [
    "你好",
    "我想了解一下你们公司的Java开发职位",
    "我有3年Java开发经验，主要做Spring Boot项目",
    "期望薪资在20-25k左右",
    "我在北京，可以接受线下工作"
  ],
  
  "挤牙膏型": [
    "嗯",
    "看看",
    "开发",
    "Java",
    "2年",
    "还行",
    "考虑下"
  ],
  
  "疯狂咨询型": [
    "你们公司有什么职位？薪资怎么样？",
    "具体有哪些技术栈？要求什么学历？",
    "公司规模多大？在哪个城市？",
    "福利待遇如何？有股权吗？",
    "面试几轮？多久能出结果？",
    "加班情况怎么样？周末要加班吗？"
  ],
  
  "质疑不信任型": [
    "你真的是AI吗？感觉像是真人",
    "你们公司靠谱吗？不会是骗子吧？",
    "这个职位是真的吗？薪资会不会虚高？",
    "你们不会套取我的信息吧？",
    "听说很多招聘都是假的，你们怎么证明？"
  ]
};

async function simulateRealConversation() {
  console.log("🎭 开始真实对话模拟测试\n");
  console.log("=" * 60);
  
  const aiServices = new AIServices(config);
  
  try {
    await aiServices.initialize();
    console.log("✅ AI服务初始化成功\n");
  } catch (error) {
    console.error("❌ 初始化失败:", error);
    return;
  }

  // 测试每种对话类型
  for (const [candidateType, messages] of Object.entries(realConversations)) {
    console.log(`\n${"=".repeat(50)}`);
    console.log(`🎯 测试场景: ${candidateType}`);
    console.log(`${"=".repeat(50)}`);
    
    const sessionId = `real-test-${Date.now()}-${candidateType}`;
    let dialogue = [
      { role: "assistant", content: "您好，我是AI招聘助手Katrina，很高兴为您服务！" }
    ];
    
    console.log("🤖 AI: 您好，我是AI招聘助手Katrina，很高兴为您服务！");
    
    // 逐句对话
    for (let i = 0; i < messages.length; i++) {
      const userMessage = messages[i];
      
      // 模拟用户思考时间
      await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
      
      console.log(`\n👤 候选人: ${userMessage}`);
      dialogue.push({ role: "user", content: userMessage });
      
      // 每3轮对话分析一次候选人类型
      if ((i + 1) % 3 === 0 || i === messages.length - 1) {
        console.log("\n🔍 AI正在分析候选人类型...");
        
        const analysisStart = Date.now();
        
        try {
          const analysis = await aiServices.analyzeCandidateType(dialogue, sessionId);
          const analysisTime = Date.now() - analysisStart;
          
          console.log(`\n📊 分析结果 (耗时: ${analysisTime}ms):`);
          console.log(`   类型: ${analysis.type}`);
          console.log(`   置信度: ${(analysis.confidence * 100).toFixed(1)}%`);
          console.log(`   主要证据: ${analysis.evidence.slice(0, 2).join('; ')}`);
          console.log(`   建议策略: ${analysis.strategy.substring(0, 50)}...`);
          console.log(`   数据源: ${analysis.source || 'AI分析'}`);
          
          // 显示费用
          const stats = aiServices.getStats();
          const monitoring = aiServices.getMonitoringStatus();
          console.log(`   💰 累计费用: ${monitoring.costMonitor.currentDailyCost.toFixed(4)}元`);
          
        } catch (error) {
          console.error(`❌ 分析失败: ${error.message}`);
        }
      }
      
      // 模拟AI回复生成时间
      if (i < messages.length - 1) {
        console.log("\n🤖 AI正在思考回复...");
        await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1500));
        
        // 简单的AI回复逻辑
        let aiReply = "";
        if (candidateType === "挤牙膏型") {
          const prompts = [
            "好的，能详细说说您的技术背景吗？",
            "请问您有几年工作经验呢？",
            "您期望的薪资范围是多少？",
            "您对哪个城市的工作比较感兴趣？"
          ];
          aiReply = prompts[i % prompts.length];
        } else if (candidateType === "疯狂咨询型") {
          const prompts = [
            "为了给您推荐最合适的职位，能先了解一下您的技术背景吗？",
            "请问您的工作经验和技术栈是什么呢？",
            "了解您的背景后，我可以为您详细介绍相关职位",
            "您方便先介绍一下自己的情况吗？"
          ];
          aiReply = prompts[i % prompts.length];
        } else if (candidateType === "质疑不信任型") {
          const prompts = [
            "我理解您的担心，我们是正规的招聘平台，您可以查看我们的资质",
            "我们的职位信息都是真实有效的，您可以先了解一下",
            "为了打消您的疑虑，我可以先介绍一下我们公司的情况",
            "您的担心很正常，我们致力于提供真实可靠的招聘服务"
          ];
          aiReply = prompts[i % prompts.length];
        } else {
          const prompts = [
            "很好！请问您具体使用哪些技术栈呢？",
            "了解了，您对哪个城市的工作比较感兴趣？",
            "好的，我为您推荐一些合适的职位",
            "您还有其他要求或疑问吗？"
          ];
          aiReply = prompts[i % prompts.length];
        }
        
        console.log(`🤖 AI: ${aiReply}`);
        dialogue.push({ role: "assistant", content: aiReply });
      }
    }
    
    // 最终分析
    console.log("\n🎯 最终候选人类型分析:");
    try {
      const finalAnalysis = await aiServices.analyzeCandidateType(dialogue, sessionId);
      console.log(`   最终类型: ${finalAnalysis.type}`);
      console.log(`   最终置信度: ${(finalAnalysis.confidence * 100).toFixed(1)}%`);
      console.log(`   是否命中缓存: ${finalAnalysis.source === 'cache' ? '是' : '否'}`);
    } catch (error) {
      console.error(`❌ 最终分析失败: ${error.message}`);
    }
    
    // 等待下一个场景
    if (Object.keys(realConversations).indexOf(candidateType) < Object.keys(realConversations).length - 1) {
      console.log("\n⏳ 等待下一个测试场景...");
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
  }
  
  // 最终统计
  console.log("\n" + "=".repeat(60));
  console.log("📈 测试完成 - 最终统计");
  console.log("=".repeat(60));
  
  const finalStats = aiServices.getStats();
  const finalMonitoring = aiServices.getMonitoringStatus();
  
  console.log(`总API调用: ${(finalStats.qwenCalls || 0) + (finalStats.deepseekCalls || 0)}次`);
  console.log(`Qwen调用: ${finalStats.qwenCalls || 0}次`);
  console.log(`DeepSeek调用: ${finalStats.deepseekCalls || 0}次`);
  console.log(`缓存命中: ${finalStats.cacheHits || 0}次`);
  console.log(`总费用: ${finalMonitoring.costMonitor.currentDailyCost.toFixed(4)}元`);
  
  const totalRequests = (finalStats.qwenCalls || 0) + (finalStats.deepseekCalls || 0) + (finalStats.cacheHits || 0);
  const cacheHitRate = totalRequests > 0 ? (finalStats.cacheHits || 0) / totalRequests : 0;
  console.log(`缓存命中率: ${(cacheHitRate * 100).toFixed(1)}%`);
  
  console.log("\n✅ 真实对话测试完成！");
}

// 运行测试
if (require.main === module) {
  simulateRealConversation().catch(console.error);
}

module.exports = { simulateRealConversation };
